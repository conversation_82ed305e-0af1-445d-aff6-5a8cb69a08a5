# Webserver

With the help of the ESP32 WIFI functionality we can implement a simple web server. 
In the example we use a Sine Wave generator as sound source and return the result as an WAV file

It would have been more elegent to use a proper __server library__ - but I did not want to introduce another dependency. So I leave this excercise up to you to implement it with less code by using your preferred library!