#include "uart_bridge.h"
#include "spp_server.h"
#include "esp_log.h"
#include "freertos/queue.h"

static const char *TAG = "UART_BRIDGE";

// 外部队列声明
extern QueueHandle_t uart_to_spp_queue;
extern QueueHandle_t spp_to_uart_queue;

esp_err_t uart_bridge_init(void)
{
    // UART配置
    uart_config_t uart_config = {
        .baud_rate = UART1_BAUD_RATE,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .rx_flow_ctrl_thresh = 122,
    };
    
    // 配置UART参数
    ESP_ERROR_CHECK(uart_param_config(UART1_PORT_NUM, &uart_config));
    
    // 设置UART引脚 (RTS/CTS disabled)
    ESP_ERROR_CHECK(uart_set_pin(UART1_PORT_NUM, UART1_TX_PIN, UART1_RX_PIN, 
                                  UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE));
    
    // 安装UART驱动
    ESP_ERROR_CHECK(uart_driver_install(UART1_PORT_NUM, BUF_SIZE * 2, 
                                         BUF_SIZE * 2, 0, NULL, 0));
    
    ESP_LOGI(TAG, "UART1 initialized on pins TX:%d RX:%d", UART1_TX_PIN, UART1_RX_PIN);
    return ESP_OK;
}

void uart_to_spp_task(void *pvParameters)
{
    uint8_t data[BUF_SIZE];
    
    while (1) {
        // 从UART读取数据
        int len = uart_read_bytes(UART1_PORT_NUM, data, BUF_SIZE, 20 / portTICK_PERIOD_MS);
        
        if (len > 0) {
            ESP_LOGI(TAG, "UART->SPP: %d bytes", len);
            
            // 发送到SPP客户端
            if (spp_server_is_connected()) {
                spp_server_send_data(data, len);
            }
        }
    }
}

void spp_to_uart_task(void *pvParameters)
{
    uint8_t byte;
    uint8_t buffer[BUF_SIZE];
    int buffer_pos = 0;
    TickType_t last_receive_time = 0;
    
    while (1) {
        // 从SPP队列接收数据
        if (xQueueReceive(spp_to_uart_queue, &byte, 10 / portTICK_PERIOD_MS)) {
            buffer[buffer_pos++] = byte;
            last_receive_time = xTaskGetTickCount();
            
            // 缓冲区满或超时，发送数据
            if (buffer_pos >= BUF_SIZE || 
                (buffer_pos > 0 && (xTaskGetTickCount() - last_receive_time) > 5)) {
                
                ESP_LOGI(TAG, "SPP->UART: %d bytes", buffer_pos);
                uart_write_bytes(UART1_PORT_NUM, buffer, buffer_pos);
                buffer_pos = 0;
            }
        } else if (buffer_pos > 0 && 
                   (xTaskGetTickCount() - last_receive_time) > 10) {
            // 超时发送
            ESP_LOGI(TAG, "SPP->UART (timeout): %d bytes", buffer_pos);
            uart_write_bytes(UART1_PORT_NUM, buffer, buffer_pos);
            buffer_pos = 0;
        }
    }
}