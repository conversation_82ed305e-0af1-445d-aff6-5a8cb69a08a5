# AudioKit
Please read the [Audio Boards Wiki](https://github.com/pschatzmann/arduino-audio-tools/wiki/Audio-Boards) first.

These sketches work on the AI Thinker AudioKit, the LyraT or any other board that is supported by the [arduino-audio-driver](https://github.com/pschatzmann/arduino-audio-driver) library. 

If you want to use them on any other board, you just pass the corresponding board variable in the constructor or you compose your own board. Further information can be found in [this Wiki](https://github.com/pschatzmann/arduino-audio-driver/wiki).

