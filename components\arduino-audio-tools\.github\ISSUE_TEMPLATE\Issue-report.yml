name: Bug report
description: Report only a bugs here! 
body:
  - type: markdown
    attributes:
      value: |
        * Before reporting a new bug please check and search the [list of existing issues](https://github.com/pschatzmann/arduino-audio-tools/issues?q=) 
        * Please check [the Readme](https://github.com/pschatzmann/arduino-audio-tools) and [Wiki](https://github.com/pschatzmann/arduino-audio-tools/wiki)
        * Don't forget to check [the discusions](https://github.com/pschatzmann/arduino-audio-tools/discussions)
        * If still experiencing the issue, please provide as many details as possible below about your hardware, computer setup and code.
  - type: textarea
    id: Description
    attributes:
      label: Problem Description
      description: Please describe your problem here and expected behaviour
      placeholder: ex. Can't connect/weird behaviour/wrong function/missing parameter..
    validations:
      required: true
  - type: textarea
    id: Board
    attributes:
      label: Device Description
      description: What development board are you using
      placeholder: e.g. ESP32 Wroom, Desktop Build, RP2040
    validations:
       required: true
  - type: textarea
    id: sketch
    attributes:
      label: Sketch
      description: Please provide full minimal sketch/code which can be run to reproduce your issue 
      placeholder: ex. Related part of the code to replicate the issue
      render: cpp
    validations:
     required: true

  - type: textarea
    id: other-remarks
    attributes:
      label: Other Steps to Reproduce 
      description: Is there any other information you can think of which will help us reproduce this problem? Any additional info can be added as well.
      placeholder: ex. I also tried on other OS, HW...it works correctly on that setup.

  - type: textarea
    id: sceanario
    attributes:
      label: What is your development environment (incl. core version info)
      description: Please provide the information about your development/runtime environment
      placeholder: Arduino ESP 2.0.14, IDF 5.3.2, STM32-Cube MX, PlatformIO ESP32 6.10.0, JupyterLab, Desktop Build

  - type: checkboxes
    id: confirmation
    attributes:
      label: I have checked existing issues, discussions and online documentation
      description: You agree to check all the resources above before opening a new issue.
      options:
        - label: I confirm I have checked existing issues, discussions and online documentation
          required: true  
