# A Simple Synthesizer for the AI Thinker AudioKit

I was taking the synthbasic example, extended it to handle multiple keys at the same time and wrapped it into a nice, easy to use class.

Here is a Synthesizer in just 30 lines of code..

### Dependencies

You need to install the following libraries:

- [Arduino Audio Tools](https://github.com/pschatzmann/arduino-audio-tools)
- [Midi](https://github.com/pschatzmann/arduino-midi)

