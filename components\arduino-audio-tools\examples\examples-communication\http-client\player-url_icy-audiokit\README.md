# A Simple Icecast Streaming Audio Player

Compared to the regular URLStream, and ICYStream provides audio Metadata.

<img src="https://pschatzmann.github.io/Resources/img/audio-toolkit.png" alt="Audio Kit" />

You dont need to bother about any wires because everything is on one nice board. Just just need to install the dependencies:

I also demonstrate how to assign your own actions to the buttons of the audio kit.

### Notes

- Do not forget to set the wifi name and password.
- The log level has been set to Info to help you to identify any problems. Please change it to AudioLogger::Warning to get the best sound quality!


### Dependencies

- https://github.com/pschatzmann/arduino-audio-tools
- https://github.com/pschatzmann/arduino-libhelix
- https://github.com/pschatzmann/arduino-audio-driver
