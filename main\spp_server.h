#ifndef SPP_SERVER_H
#define SPP_SERVER_H

#include "esp_bt.h"
#include "esp_bt_main.h"
#include "esp_gap_bt_api.h"
#include "esp_spp_api.h"

#ifdef __cplusplus
extern "C" {
#endif

// SPP服务器初始化
esp_err_t spp_server_init(void);

// 发送数据到SPP客户端
esp_err_t spp_server_send_data(uint8_t *data, uint16_t len);

// SPP连接状态
bool spp_server_is_connected(void);

#ifdef __cplusplus
}
#endif

#endif // SPP_SERVER_H