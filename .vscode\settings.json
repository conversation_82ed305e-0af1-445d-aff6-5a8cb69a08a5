{"idf.pythonInstallPath": "D:\\ESP32\\.espressif\\tools\\idf-python\\3.11.2\\python.exe", "idf.flashType": "UART", "idf.portWin": "COM3", "idf.customExtraVars": {"IDF_TARGET": "esp32"}, "clangd.path": "D:\\ESP32\\.espressif\\tools\\esp-clang\\15.0.0-23786128ae\\esp-clang\\bin\\clangd.exe", "clangd.arguments": ["--background-index", "--query-driver=D:\\ESP32\\.espressif\\tools\\xtensa-esp32-elf\\esp-12.2.0_20230208\\xtensa-esp32-elf\\bin\\xtensa-esp32-elf-gcc.exe", "--compile-commands-dir=d:\\ESP32\\Projects\\esp32-a2dp-sink\\build"]}