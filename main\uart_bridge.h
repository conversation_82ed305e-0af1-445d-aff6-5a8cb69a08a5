#ifndef UART_BRIDGE_H
#define UART_BRIDGE_H

#include "driver/uart.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

#ifdef __cplusplus
extern "C" {
#endif

#define UART1_PORT_NUM      UART_NUM_1
#define UART1_BAUD_RATE     115200
#define UART1_TX_PIN        19
#define UART1_RX_PIN        18
#define UART1_RTS_PIN       16      // disabled
#define UART1_CTS_PIN       17      // disabled

#define BUF_SIZE            1024

// UART桥接初始化
esp_err_t uart_bridge_init(void);

// 数据转发任务
void uart_to_spp_task(void *pvParameters);
void spp_to_uart_task(void *pvParameters);

#ifdef __cplusplus
}
#endif

#endif // UART_BRIDGE_H