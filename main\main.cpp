#include <stdio.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "driver/uart.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "esp_bt.h"
#include "esp_bt_main.h"

#include "BluetoothA2DPSinkQueued.h"
#include "spp_server.h"
#include "uart_bridge.h"

// 统一的蓝牙设备名称定义
const char* BT_DEVICE_NAME = "CYAUDIO";

I2SStream out;
BluetoothA2DPSinkQueued a2dp_sink(out);

// 新增全局变量
QueueHandle_t uart_to_spp_queue;
QueueHandle_t spp_to_uart_queue;

extern "C" void app_main(void)
{
    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    
    // 初始化队列
    uart_to_spp_queue = xQueueCreate(256, sizeof(uint8_t));
    spp_to_uart_queue = xQueueCreate(256, sizeof(uint8_t));
    
    // 初始化UART桥接
    uart_bridge_init();
    
    // 等待系统稳定
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    // 配置I2S输出流 - 使用更小的缓冲区以减少内存使用
    auto config = out.defaultConfig(TX_MODE);
    config.sample_rate = 44100;
    config.bits_per_sample = 16;
    config.channels = 2;
    config.pin_bck = 13;
    config.pin_ws = 15;
    config.pin_data = 2;
    config.buffer_count = 4;
    config.buffer_size = 512;
    config.use_apll = false;
    config.auto_clear = true;
    
    // 延迟以确保系统稳定
    vTaskDelay(pdMS_TO_TICKS(500));
    
    if (!out.begin(config)) {
        ESP_LOGE("MAIN", "Failed to initialize I2S");
        return;
    }
    ESP_LOGI("MAIN", "I2S initialized successfully");
    
    // 启动A2DP
    a2dp_sink.set_auto_reconnect(true);
    a2dp_sink.set_task_core(0);
    a2dp_sink.start(BT_DEVICE_NAME);
    
    // 等待蓝牙栈初始化完成
    vTaskDelay(pdMS_TO_TICKS(3000));
    
    // 初始化SPP服务器
    spp_server_init();
    
    // 启动数据桥接任务
    xTaskCreate(uart_to_spp_task, "uart_to_spp", 2048, NULL, 5, NULL);
    xTaskCreate(spp_to_uart_task, "spp_to_uart", 2048, NULL, 5, NULL);
}
