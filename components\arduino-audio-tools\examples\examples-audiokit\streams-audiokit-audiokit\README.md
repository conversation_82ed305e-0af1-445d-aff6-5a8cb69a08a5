
# Stream Input from an AudioKit to AudioKit Output

## General Description:

We implement a AudioKit source and sink: We stream the sound input which we read in from the I2S interface and write it back via I2S to the Speaker 

<img src="https://pschatzmann.github.io/Resources/img/audio-toolkit.png" alt="Audio Kit" />

### Dependencies

You need to install the following libraries:

- https://github.com/pschatzmann/arduino-audio-tools
- https://github.com/pschatzmann/arduino-audio-driver
