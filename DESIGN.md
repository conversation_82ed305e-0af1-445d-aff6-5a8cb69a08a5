
### 2.2 功能模块
1. **A2DP音频模块**：接收蓝牙音频流，输出到I2S
2. **SPP串口模块**：提供蓝牙串口服务
3. **UART1模块**：硬件串口通信
4. **数据桥接模块**：UART1与SPP之间的双向数据转发

## 3. 硬件配置

### 3.1 引脚分配
```cpp
// I2S音频输出（现有配置）
#define I2S_WS_PIN    12
#define I2S_BCK_PIN   13
#define I2S_DATA_PIN  15

// UART1配置（新增）
#define UART1_TX_PIN  17
#define UART1_RX_PIN  16
#define UART1_RTS_PIN 18  // 可选，硬件流控
#define UART1_CTS_PIN 19  // 可选，硬件流控
```

### 3.2 UART1参数
- **波特率**：115200 bps（可配置）
- **数据位**：8位
- **停止位**：1位
- **校验位**：无
- **流控制**：可选RTS/CTS硬件流控

## 4. 软件配置

### 4.1 sdkconfig配置修改

需要在 `sdkconfig.defaults` 中添加以下配置：

```ini
# 启用SPP功能
CONFIG_BT_SPP_ENABLED=y

# 启用BLE功能（可选，用于更好的iOS兼容性）
CONFIG_BT_BLE_ENABLED=y

# UART配置
CONFIG_UART_ISR_IN_IRAM=y

# 增加任务栈大小
CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH=3072
```

### 4.2 CMakeLists.txt修改

修改 `main/CMakeLists.txt`：

```cmake
# Edit following two lines to set component requirements (see docs)
set(COMPONENT_REQUIRES driver)
set(COMPONENT_PRIV_REQUIRES )

set(COMPONENT_SRCS "main.cpp" "uart_bridge.cpp" "spp_server.cpp")
set(COMPONENT_ADD_INCLUDEDIRS "")

register_component()
```

## 5. 代码实现

### 5.1 主程序结构

修改 `main/main.cpp`：

```cpp
#include <stdio.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "driver/uart.h"
#include "esp_log.h"

#include "BluetoothA2DPSinkQueued.h"
#include "spp_server.h"
#include "uart_bridge.h"

I2SStream out;
BluetoothA2DPSinkQueued a2dp_sink(out);

// 新增全局变量
QueueHandle_t uart_to_spp_queue;
QueueHandle_t spp_to_uart_queue;

extern "C" void app_main(void)
{
    // 初始化队列
    uart_to_spp_queue = xQueueCreate(256, sizeof(uint8_t));
    spp_to_uart_queue = xQueueCreate(256, sizeof(uint8_t));
    
    // 现有I2S和A2DP代码
    I2SConfig i2s_config;
    i2s_config.pin_ws = 12;
    i2s_config.pin_bck = 13;
    i2s_config.pin_data = 15;
    out.begin(i2s_config);
    
    // 初始化UART1
    uart_bridge_init();
    
    // 初始化SPP服务器
    spp_server_init();
    
    // 启动数据桥接任务
    xTaskCreate(uart_to_spp_task, "uart_to_spp", 2048, NULL, 5, NULL);
    xTaskCreate(spp_to_uart_task, "spp_to_uart", 2048, NULL, 5, NULL);
    
    // 启动A2DP
    a2dp_sink.set_auto_reconnect(true);
    a2dp_sink.set_task_core(0);
    a2dp_sink.start("CYAUDIO");
}
```

### 5.2 SPP服务器实现

创建 `main/spp_server.h`：

```cpp
#ifndef SPP_SERVER_H
#define SPP_SERVER_H

#include "esp_bt.h"
#include "esp_bt_main.h"
#include "esp_gap_bt_api.h"
#include "esp_spp_api.h"

#ifdef __cplusplus
extern "C" {
#endif

// SPP服务器初始化
esp_err_t spp_server_init(void);

// 发送数据到SPP客户端
esp_err_t spp_server_send_data(uint8_t *data, uint16_t len);

// SPP连接状态
bool spp_server_is_connected(void);

#ifdef __cplusplus
}
#endif

#endif // SPP_SERVER_H
```

创建 `main/spp_server.cpp`：

```cpp
#include "spp_server.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"

static const char *TAG = "SPP_SERVER";

// 外部队列声明
extern QueueHandle_t spp_to_uart_queue;

// SPP连接句柄
static uint32_t spp_handle = 0;
static bool spp_connected = false;

// SPP事件回调
static void esp_spp_cb(esp_spp_cb_event_t event, esp_spp_cb_param_t *param)
{
    switch (event) {
    case ESP_SPP_INIT_EVT:
        ESP_LOGI(TAG, "ESP_SPP_INIT_EVT");
        esp_spp_start_srv(ESP_SPP_SEC_AUTHENTICATE, ESP_SPP_ROLE_SLAVE, 0, "ESP32_SPP_SERVER");
        break;
        
    case ESP_SPP_START_EVT:
        ESP_LOGI(TAG, "ESP_SPP_START_EVT");
        break;
        
    case ESP_SPP_SRV_OPEN_EVT:
        ESP_LOGI(TAG, "ESP_SPP_SRV_OPEN_EVT");
        spp_handle = param->srv_open.handle;
        spp_connected = true;
        break;
        
    case ESP_SPP_CLOSE_EVT:
        ESP_LOGI(TAG, "ESP_SPP_CLOSE_EVT");
        spp_connected = false;
        break;
        
    case ESP_SPP_DATA_IND_EVT:
        // 接收到SPP数据，转发到UART队列
        for (int i = 0; i < param->data_ind.len; i++) {
            uint8_t byte = param->data_ind.data[i];
            xQueueSend(spp_to_uart_queue, &byte, 0);
        }
        break;
        
    default:
        break;
    }
}

esp_err_t spp_server_init(void)
{
    esp_err_t ret;
    
    // 初始化SPP
    ret = esp_spp_register_callback(esp_spp_cb);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "spp register failed: %s", esp_err_to_name(ret));
        return ret;
    }
    
    ret = esp_spp_init(ESP_SPP_MODE_CB);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "spp init failed: %s", esp_err_to_name(ret));
        return ret;
    }
    
    ESP_LOGI(TAG, "SPP server initialized");
    return ESP_OK;
}

esp_err_t spp_server_send_data(uint8_t *data, uint16_t len)
{
    if (!spp_connected) {
        return ESP_ERR_INVALID_STATE;
    }
    
    return esp_spp_write(spp_handle, len, data);
}

bool spp_server_is_connected(void)
{
    return spp_connected;
}
```

### 5.3 UART桥接实现

创建 `main/uart_bridge.h`：

```cpp
#ifndef UART_BRIDGE_H
#define UART_BRIDGE_H

#include "driver/uart.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

#ifdef __cplusplus
extern "C" {
#endif

#define UART1_PORT_NUM      UART_NUM_1
#define UART1_BAUD_RATE     115200
#define UART1_TX_PIN        17
#define UART1_RX_PIN        16
#define UART1_RTS_PIN       18
#define UART1_CTS_PIN       19

#define BUF_SIZE            1024

// UART桥接初始化
esp_err_t uart_bridge_init(void);

// 数据转发任务
void uart_to_spp_task(void *pvParameters);
void spp_to_uart_task(void *pvParameters);

#ifdef __cplusplus
}
#endif

#endif // UART_BRIDGE_H
```

创建 `main/uart_bridge.cpp`：

```cpp
#include "uart_bridge.h"
#include "spp_server.h"
#include "esp_log.h"
#include "freertos/queue.h"

static const char *TAG = "UART_BRIDGE";

// 外部队列声明
extern QueueHandle_t uart_to_spp_queue;
extern QueueHandle_t spp_to_uart_queue;

esp_err_t uart_bridge_init(void)
{
    // UART配置
    uart_config_t uart_config = {
        .baud_rate = UART1_BAUD_RATE,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_CTS_RTS,
        .rx_flow_ctrl_thresh = 122,
    };
    
    // 配置UART参数
    ESP_ERROR_CHECK(uart_param_config(UART1_PORT_NUM, &uart_config));
    
    // 设置UART引脚
    ESP_ERROR_CHECK(uart_set_pin(UART1_PORT_NUM, UART1_TX_PIN, UART1_RX_PIN, 
                                  UART1_RTS_PIN, UART1_CTS_PIN));
    
    // 安装UART驱动
    ESP_ERROR_CHECK(uart_driver_install(UART1_PORT_NUM, BUF_SIZE * 2, 
                                         BUF_SIZE * 2, 0, NULL, 0));
    
    ESP_LOGI(TAG, "UART1 initialized on pins TX:%d RX:%d", UART1_TX_PIN, UART1_RX_PIN);
    return ESP_OK;
}

void uart_to_spp_task(void *pvParameters)
{
    uint8_t data[BUF_SIZE];
    
    while (1) {
        // 从UART读取数据
        int len = uart_read_bytes(UART1_PORT_NUM, data, BUF_SIZE, 20 / portTICK_PERIOD_MS);
        
        if (len > 0) {
            ESP_LOGI(TAG, "UART->SPP: %d bytes", len);
            
            // 发送到SPP客户端
            if (spp_server_is_connected()) {
                spp_server_send_data(data, len);
            }
        }
    }
}

void spp_to_uart_task(void *pvParameters)
{
    uint8_t byte;
    uint8_t buffer[BUF_SIZE];
    int buffer_pos = 0;
    TickType_t last_receive_time = 0;
    
    while (1) {
        // 从SPP队列接收数据
        if (xQueueReceive(spp_to_uart_queue, &byte, 10 / portTICK_PERIOD_MS)) {
            buffer[buffer_pos++] = byte;
            last_receive_time = xTaskGetTickCount();
            
            // 缓冲区满或超时，发送数据
            if (buffer_pos >= BUF_SIZE || 
                (buffer_pos > 0 && (xTaskGetTickCount() - last_receive_time) > 5)) {
                
                ESP_LOGI(TAG, "SPP->UART: %d bytes", buffer_pos);
                uart_write_bytes(UART1_PORT_NUM, buffer, buffer_pos);
                buffer_pos = 0;
            }
        } else if (buffer_pos > 0 && 
                   (xTaskGetTickCount() - last_receive_time) > 10) {
            // 超时发送
            ESP_LOGI(TAG, "SPP->UART (timeout): %d bytes", buffer_pos);
            uart_write_bytes(UART1_PORT_NUM, buffer, buffer_pos);
            buffer_pos = 0;
        }
    }
}
```

## 6. 兼容性说明

### 6.1 Android设备
- **A2DP**：完全支持，可播放音频
- **SPP**：原生支持，无需额外配置
- **连接方式**：可同时连接A2DP和SPP

### 6.2 iOS设备
- **A2DP**：完全支持，可播放音频
- **SPP**：需要MFi认证，建议使用BLE UART替代
- **解决方案**：可在后续版本中添加BLE UART Service

## 7. 测试方案

### 7.1 功能测试
1. **A2DP测试**：连接手机播放音频，验证音频输出正常
2. **SPP连接测试**：使用蓝牙串口调试工具连接ESP32
3. **UART测试**：连接外部设备到UART1，测试数据收发
4. **桥接测试**：验证UART1与SPP之间的双向数据传输

### 7.2 性能测试
- **数据吞吐量**：测试UART1与SPP的最大传输速率
- **延迟测试**：测量数据转发延迟
- **稳定性测试**：长时间运行测试

## 8. 部署说明

### 8.1 编译步骤
```bash
idf.py build
```

### 8.2 烧录步骤
```bash
idf.py flash monitor
```

### 8.3 配置步骤
1. 上电后ESP32自动启动A2DP Sink和SPP服务
2. 手机搜索并连接"CYAUDIO"进行音频播放
3. 使用蓝牙串口工具连接"ESP32_SPP_SERVER"进行数据通信
4. 连接外部设备到UART1进行串口通信

## 9. 扩展功能

### 9.1 BLE UART Service（可选）
为了更好地支持iOS设备，可以添加BLE UART Service：

```cpp
// 在sdkconfig.defaults中启用
CONFIG_BT_BLE_ENABLED=y
CONFIG_BT_GATTS_ENABLE=y
```

### 9.2 数据处理功能
- **数据过滤**：根据协议过滤特定数据
- **数据转换**：格式转换和编码转换
- **数据缓存**：大数据包的分片处理

## 10. 故障排除

### 10.1 常见问题
1. **SPP连接失败**：检查配置是否启用SPP
2. **UART数据丢失**：增加缓冲区大小或调整波特率
3. **音频播放异常**：检查I2S引脚配置

### 10.2 调试方法
- 使用ESP_LOGI输出调试信息
- 监控串口输出查看系统状态
- 使用蓝牙抓包工具分析通信过程

## 11. 文件结构
