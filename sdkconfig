#
# Automatically generated file. DO NOT EDIT.
# Espressif IoT Development Framework (ESP-IDF) 5.1.4 Project Configuration
#
CONFIG_SOC_BROWNOUT_RESET_SUPPORTED="Not determined"
CONFIG_SOC_TWAI_BRP_DIV_SUPPORTED="Not determined"
CONFIG_SOC_DPORT_WORKAROUND="Not determined"
CONFIG_SOC_CAPS_ECO_VER_MAX=301
CONFIG_SOC_ADC_SUPPORTED=y
CONFIG_SOC_DAC_SUPPORTED=y
CONFIG_SOC_UART_SUPPORTED=y
CONFIG_SOC_MCPWM_SUPPORTED=y
CONFIG_SOC_GPTIMER_SUPPORTED=y
CONFIG_SOC_SDMMC_HOST_SUPPORTED=y
CONFIG_SOC_BT_SUPPORTED=y
CONFIG_SOC_PCNT_SUPPORTED=y
CONFIG_SOC_WIFI_SUPPORTED=y
CONFIG_SOC_SDIO_SLAVE_SUPPORTED=y
CONFIG_SOC_TWAI_SUPPORTED=y
CONFIG_SOC_EMAC_SUPPORTED=y
CONFIG_SOC_ULP_SUPPORTED=y
CONFIG_SOC_CCOMP_TIMER_SUPPORTED=y
CONFIG_SOC_RTC_FAST_MEM_SUPPORTED=y
CONFIG_SOC_RTC_SLOW_MEM_SUPPORTED=y
CONFIG_SOC_RTC_MEM_SUPPORTED=y
CONFIG_SOC_I2S_SUPPORTED=y
CONFIG_SOC_RMT_SUPPORTED=y
CONFIG_SOC_SDM_SUPPORTED=y
CONFIG_SOC_GPSPI_SUPPORTED=y
CONFIG_SOC_LEDC_SUPPORTED=y
CONFIG_SOC_I2C_SUPPORTED=y
CONFIG_SOC_SUPPORT_COEXISTENCE=y
CONFIG_SOC_AES_SUPPORTED=y
CONFIG_SOC_MPI_SUPPORTED=y
CONFIG_SOC_SHA_SUPPORTED=y
CONFIG_SOC_FLASH_ENC_SUPPORTED=y
CONFIG_SOC_SECURE_BOOT_SUPPORTED=y
CONFIG_SOC_TOUCH_SENSOR_SUPPORTED=y
CONFIG_SOC_BOD_SUPPORTED=y
CONFIG_SOC_ULP_FSM_SUPPORTED=y
CONFIG_SOC_DPORT_WORKAROUND_DIS_INTERRUPT_LVL=5
CONFIG_SOC_XTAL_SUPPORT_26M=y
CONFIG_SOC_XTAL_SUPPORT_40M=y
CONFIG_SOC_XTAL_SUPPORT_AUTO_DETECT=y
CONFIG_SOC_ADC_RTC_CTRL_SUPPORTED=y
CONFIG_SOC_ADC_DIG_CTRL_SUPPORTED=y
CONFIG_SOC_ADC_DMA_SUPPORTED=y
CONFIG_SOC_ADC_PERIPH_NUM=2
CONFIG_SOC_ADC_MAX_CHANNEL_NUM=10
CONFIG_SOC_ADC_ATTEN_NUM=4
CONFIG_SOC_ADC_DIGI_CONTROLLER_NUM=2
CONFIG_SOC_ADC_PATT_LEN_MAX=16
CONFIG_SOC_ADC_DIGI_MIN_BITWIDTH=9
CONFIG_SOC_ADC_DIGI_MAX_BITWIDTH=12
CONFIG_SOC_ADC_DIGI_RESULT_BYTES=2
CONFIG_SOC_ADC_DIGI_DATA_BYTES_PER_CONV=4
CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_HIGH=2
CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_LOW=20
CONFIG_SOC_ADC_RTC_MIN_BITWIDTH=9
CONFIG_SOC_ADC_RTC_MAX_BITWIDTH=12
CONFIG_SOC_SHARED_IDCACHE_SUPPORTED=y
CONFIG_SOC_IDCACHE_PER_CORE=y
CONFIG_SOC_CPU_CORES_NUM=2
CONFIG_SOC_CPU_INTR_NUM=32
CONFIG_SOC_CPU_HAS_FPU=y
CONFIG_SOC_CPU_BREAKPOINTS_NUM=2
CONFIG_SOC_CPU_WATCHPOINTS_NUM=2
CONFIG_SOC_CPU_WATCHPOINT_MAX_REGION_SIZE=64
CONFIG_SOC_DAC_CHAN_NUM=2
CONFIG_SOC_DAC_RESOLUTION=8
CONFIG_SOC_DAC_DMA_16BIT_ALIGN=y
CONFIG_SOC_GPIO_PORT=1
CONFIG_SOC_GPIO_PIN_COUNT=40
CONFIG_SOC_GPIO_VALID_GPIO_MASK=0xFFFFFFFFFF
CONFIG_SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK=0xEF0FEA
CONFIG_SOC_I2C_NUM=2
CONFIG_SOC_I2C_FIFO_LEN=32
CONFIG_SOC_I2C_CMD_REG_NUM=16
CONFIG_SOC_I2C_SUPPORT_SLAVE=y
CONFIG_SOC_I2C_SUPPORT_APB=y
CONFIG_SOC_I2S_NUM=2
CONFIG_SOC_I2S_HW_VERSION_1=y
CONFIG_SOC_I2S_SUPPORTS_APLL=y
CONFIG_SOC_I2S_SUPPORTS_PLL_F160M=y
CONFIG_SOC_I2S_SUPPORTS_PDM=y
CONFIG_SOC_I2S_SUPPORTS_PDM_TX=y
CONFIG_SOC_I2S_PDM_MAX_TX_LINES=1
CONFIG_SOC_I2S_SUPPORTS_PDM_RX=y
CONFIG_SOC_I2S_PDM_MAX_RX_LINES=1
CONFIG_SOC_I2S_SUPPORTS_ADC_DAC=y
CONFIG_SOC_I2S_SUPPORTS_ADC=y
CONFIG_SOC_I2S_SUPPORTS_DAC=y
CONFIG_SOC_I2S_SUPPORTS_LCD_CAMERA=y
CONFIG_SOC_I2S_TRANS_SIZE_ALIGN_WORD=y
CONFIG_SOC_I2S_LCD_I80_VARIANT=y
CONFIG_SOC_LCD_I80_SUPPORTED=y
CONFIG_SOC_LCD_I80_BUSES=2
CONFIG_SOC_LCD_I80_BUS_WIDTH=24
CONFIG_SOC_LEDC_HAS_TIMER_SPECIFIC_MUX=y
CONFIG_SOC_LEDC_SUPPORT_APB_CLOCK=y
CONFIG_SOC_LEDC_SUPPORT_REF_TICK=y
CONFIG_SOC_LEDC_SUPPORT_HS_MODE=y
CONFIG_SOC_LEDC_CHANNEL_NUM=8
CONFIG_SOC_LEDC_TIMER_BIT_WIDTH=20
CONFIG_SOC_MCPWM_GROUPS=2
CONFIG_SOC_MCPWM_TIMERS_PER_GROUP=3
CONFIG_SOC_MCPWM_OPERATORS_PER_GROUP=3
CONFIG_SOC_MCPWM_COMPARATORS_PER_OPERATOR=2
CONFIG_SOC_MCPWM_GENERATORS_PER_OPERATOR=2
CONFIG_SOC_MCPWM_TRIGGERS_PER_OPERATOR=2
CONFIG_SOC_MCPWM_GPIO_FAULTS_PER_GROUP=3
CONFIG_SOC_MCPWM_CAPTURE_TIMERS_PER_GROUP=y
CONFIG_SOC_MCPWM_CAPTURE_CHANNELS_PER_TIMER=3
CONFIG_SOC_MCPWM_GPIO_SYNCHROS_PER_GROUP=3
CONFIG_SOC_MMU_PERIPH_NUM=2
CONFIG_SOC_MMU_LINEAR_ADDRESS_REGION_NUM=3
CONFIG_SOC_MPU_MIN_REGION_SIZE=0x20000000
CONFIG_SOC_MPU_REGIONS_MAX_NUM=8
CONFIG_SOC_PCNT_GROUPS=1
CONFIG_SOC_PCNT_UNITS_PER_GROUP=8
CONFIG_SOC_PCNT_CHANNELS_PER_UNIT=2
CONFIG_SOC_PCNT_THRES_POINT_PER_UNIT=2
CONFIG_SOC_RMT_GROUPS=1
CONFIG_SOC_RMT_TX_CANDIDATES_PER_GROUP=8
CONFIG_SOC_RMT_RX_CANDIDATES_PER_GROUP=8
CONFIG_SOC_RMT_CHANNELS_PER_GROUP=8
CONFIG_SOC_RMT_MEM_WORDS_PER_CHANNEL=64
CONFIG_SOC_RMT_SUPPORT_REF_TICK=y
CONFIG_SOC_RMT_SUPPORT_APB=y
CONFIG_SOC_RMT_CHANNEL_CLK_INDEPENDENT=y
CONFIG_SOC_RTCIO_PIN_COUNT=18
CONFIG_SOC_RTCIO_INPUT_OUTPUT_SUPPORTED=y
CONFIG_SOC_RTCIO_HOLD_SUPPORTED=y
CONFIG_SOC_RTCIO_WAKE_SUPPORTED=y
CONFIG_SOC_SDM_GROUPS=1
CONFIG_SOC_SDM_CHANNELS_PER_GROUP=8
CONFIG_SOC_SDM_CLK_SUPPORT_APB=y
CONFIG_SOC_SPI_HD_BOTH_INOUT_SUPPORTED=y
CONFIG_SOC_SPI_AS_CS_SUPPORTED=y
CONFIG_SOC_SPI_PERIPH_NUM=3
CONFIG_SOC_SPI_DMA_CHAN_NUM=2
CONFIG_SOC_SPI_MAX_CS_NUM=3
CONFIG_SOC_SPI_SUPPORT_CLK_APB=y
CONFIG_SOC_SPI_MAXIMUM_BUFFER_SIZE=64
CONFIG_SOC_SPI_MAX_PRE_DIVIDER=8192
CONFIG_SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED=y
CONFIG_SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED=y
CONFIG_SOC_MEMSPI_SRC_FREQ_26M_SUPPORTED=y
CONFIG_SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED=y
CONFIG_SOC_TIMER_GROUPS=2
CONFIG_SOC_TIMER_GROUP_TIMERS_PER_GROUP=2
CONFIG_SOC_TIMER_GROUP_COUNTER_BIT_WIDTH=64
CONFIG_SOC_TIMER_GROUP_TOTAL_TIMERS=4
CONFIG_SOC_TIMER_GROUP_SUPPORT_APB=y
CONFIG_SOC_TOUCH_VERSION_1=y
CONFIG_SOC_TOUCH_SENSOR_NUM=10
CONFIG_SOC_TOUCH_PAD_MEASURE_WAIT_MAX=0xFF
CONFIG_SOC_TWAI_CONTROLLER_NUM=1
CONFIG_SOC_TWAI_BRP_MIN=2
CONFIG_SOC_TWAI_CLK_SUPPORT_APB=y
CONFIG_SOC_TWAI_SUPPORT_MULTI_ADDRESS_LAYOUT=y
CONFIG_SOC_UART_NUM=3
CONFIG_SOC_UART_SUPPORT_APB_CLK=y
CONFIG_SOC_UART_SUPPORT_REF_TICK=y
CONFIG_SOC_UART_FIFO_LEN=128
CONFIG_SOC_UART_BITRATE_MAX=5000000
CONFIG_SOC_SPIRAM_SUPPORTED=y
CONFIG_SOC_SPI_MEM_SUPPORT_CONFIG_GPIO_BY_EFUSE=y
CONFIG_SOC_SHA_SUPPORT_PARALLEL_ENG=y
CONFIG_SOC_SHA_SUPPORT_SHA1=y
CONFIG_SOC_SHA_SUPPORT_SHA256=y
CONFIG_SOC_SHA_SUPPORT_SHA384=y
CONFIG_SOC_SHA_SUPPORT_SHA512=y
CONFIG_SOC_RSA_MAX_BIT_LEN=4096
CONFIG_SOC_AES_SUPPORT_AES_128=y
CONFIG_SOC_AES_SUPPORT_AES_192=y
CONFIG_SOC_AES_SUPPORT_AES_256=y
CONFIG_SOC_SECURE_BOOT_V1=y
CONFIG_SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS=y
CONFIG_SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX=32
CONFIG_SOC_PHY_DIG_REGS_MEM_SIZE=21
CONFIG_SOC_PM_SUPPORT_EXT0_WAKEUP=y
CONFIG_SOC_PM_SUPPORT_EXT1_WAKEUP=y
CONFIG_SOC_PM_SUPPORT_EXT_WAKEUP=y
CONFIG_SOC_PM_SUPPORT_TOUCH_SENSOR_WAKEUP=y
CONFIG_SOC_PM_SUPPORT_RTC_PERIPH_PD=y
CONFIG_SOC_PM_SUPPORT_RTC_FAST_MEM_PD=y
CONFIG_SOC_PM_SUPPORT_RTC_SLOW_MEM_PD=y
CONFIG_SOC_PM_SUPPORT_RC_FAST_PD=y
CONFIG_SOC_PM_SUPPORT_VDDSDIO_PD=y
CONFIG_SOC_PM_SUPPORT_MODEM_PD=y
CONFIG_SOC_CONFIGURABLE_VDDSDIO_SUPPORTED=y
CONFIG_SOC_CLK_APLL_SUPPORTED=y
CONFIG_SOC_APLL_MULTIPLIER_OUT_MIN_HZ=350000000
CONFIG_SOC_APLL_MULTIPLIER_OUT_MAX_HZ=500000000
CONFIG_SOC_APLL_MIN_HZ=5303031
CONFIG_SOC_APLL_MAX_HZ=125000000
CONFIG_SOC_CLK_RC_FAST_D256_SUPPORTED=y
CONFIG_SOC_RTC_SLOW_CLK_SUPPORT_RC_FAST_D256=y
CONFIG_SOC_CLK_RC_FAST_SUPPORT_CALIBRATION=y
CONFIG_SOC_CLK_XTAL32K_SUPPORTED=y
CONFIG_SOC_SDMMC_USE_IOMUX=y
CONFIG_SOC_SDMMC_NUM_SLOTS=2
CONFIG_SOC_WIFI_WAPI_SUPPORT=y
CONFIG_SOC_WIFI_CSI_SUPPORT=y
CONFIG_SOC_WIFI_MESH_SUPPORT=y
CONFIG_SOC_WIFI_SUPPORT_VARIABLE_BEACON_WINDOW=y
CONFIG_SOC_WIFI_NAN_SUPPORT=y
CONFIG_SOC_BLE_SUPPORTED=y
CONFIG_SOC_BLE_MESH_SUPPORTED=y
CONFIG_SOC_BT_CLASSIC_SUPPORTED=y
CONFIG_SOC_BLUFI_SUPPORTED=y
CONFIG_SOC_ULP_HAS_ADC=y
CONFIG_SOC_PHY_COMBO_MODULE=y
CONFIG_IDF_CMAKE=y
CONFIG_IDF_TARGET_ARCH_XTENSA=y
CONFIG_IDF_TARGET_ARCH="xtensa"
CONFIG_IDF_TARGET="esp32"
CONFIG_IDF_TARGET_ESP32=y
CONFIG_IDF_FIRMWARE_CHIP_ID=0x0000

#
# Build type
#
CONFIG_APP_BUILD_TYPE_APP_2NDBOOT=y
# CONFIG_APP_BUILD_TYPE_RAM is not set
CONFIG_APP_BUILD_GENERATE_BINARIES=y
CONFIG_APP_BUILD_BOOTLOADER=y
CONFIG_APP_BUILD_USE_FLASH_SECTIONS=y
# default:
# CONFIG_APP_REPRODUCIBLE_BUILD is not set
# default:
# CONFIG_APP_NO_BLOBS is not set
# default:
# CONFIG_APP_COMPATIBLE_PRE_V2_1_BOOTLOADERS is not set
# default:
# CONFIG_APP_COMPATIBLE_PRE_V3_1_BOOTLOADERS is not set
# end of Build type

#
# Bootloader config
#
CONFIG_BOOTLOADER_OFFSET_IN_FLASH=0x1000
CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_SIZE=y
# CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG is not set
# CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_PERF is not set
# CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_NONE is not set
# CONFIG_BOOTLOADER_LOG_LEVEL_NONE is not set
# CONFIG_BOOTLOADER_LOG_LEVEL_ERROR is not set
# CONFIG_BOOTLOADER_LOG_LEVEL_WARN is not set
CONFIG_BOOTLOADER_LOG_LEVEL_INFO=y
# CONFIG_BOOTLOADER_LOG_LEVEL_DEBUG is not set
# CONFIG_BOOTLOADER_LOG_LEVEL_VERBOSE is not set
CONFIG_BOOTLOADER_LOG_LEVEL=3

#
# Serial Flash Configurations
#
# default:
# CONFIG_BOOTLOADER_FLASH_DC_AWARE is not set
CONFIG_BOOTLOADER_FLASH_XMC_SUPPORT=y
# end of Serial Flash Configurations

CONFIG_BOOTLOADER_VDDSDIO_BOOST_1_9V=y
# default:
# CONFIG_BOOTLOADER_FACTORY_RESET is not set
# default:
# CONFIG_BOOTLOADER_APP_TEST is not set
CONFIG_BOOTLOADER_REGION_PROTECTION_ENABLE=y
CONFIG_BOOTLOADER_WDT_ENABLE=y
# default:
# CONFIG_BOOTLOADER_WDT_DISABLE_IN_USER_CODE is not set
CONFIG_BOOTLOADER_WDT_TIME_MS=9000
# default:
# CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE is not set
# default:
# CONFIG_BOOTLOADER_SKIP_VALIDATE_IN_DEEP_SLEEP is not set
# default:
# CONFIG_BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON is not set
# default:
# CONFIG_BOOTLOADER_SKIP_VALIDATE_ALWAYS is not set
CONFIG_BOOTLOADER_RESERVE_RTC_SIZE=0
# default:
# CONFIG_BOOTLOADER_CUSTOM_RESERVE_RTC is not set
# end of Bootloader config

#
# Security features
#
CONFIG_SECURE_BOOT_V1_SUPPORTED=y
# default:
# CONFIG_SECURE_SIGNED_APPS_NO_SECURE_BOOT is not set
# default:
# CONFIG_SECURE_BOOT is not set
# default:
# CONFIG_SECURE_FLASH_ENC_ENABLED is not set
# end of Security features

#
# Application manager
#
CONFIG_APP_COMPILE_TIME_DATE=y
# default:
# CONFIG_APP_EXCLUDE_PROJECT_VER_VAR is not set
# default:
# CONFIG_APP_EXCLUDE_PROJECT_NAME_VAR is not set
# default:
# CONFIG_APP_PROJECT_VER_FROM_CONFIG is not set
CONFIG_APP_RETRIEVE_LEN_ELF_SHA=16
# end of Application manager

CONFIG_ESP_ROM_HAS_CRC_LE=y
CONFIG_ESP_ROM_HAS_CRC_BE=y
CONFIG_ESP_ROM_HAS_MZ_CRC32=y
CONFIG_ESP_ROM_HAS_JPEG_DECODE=y
CONFIG_ESP_ROM_HAS_UART_BUF_SWITCH=y
CONFIG_ESP_ROM_NEEDS_SWSETUP_WORKAROUND=y
CONFIG_ESP_ROM_HAS_NEWLIB_NANO_FORMAT=y

#
# Serial flasher config
#
# default:
# CONFIG_ESPTOOLPY_NO_STUB is not set
# CONFIG_ESPTOOLPY_FLASHMODE_QIO is not set
# CONFIG_ESPTOOLPY_FLASHMODE_QOUT is not set
CONFIG_ESPTOOLPY_FLASHMODE_DIO=y
# CONFIG_ESPTOOLPY_FLASHMODE_DOUT is not set
CONFIG_ESPTOOLPY_FLASH_SAMPLE_MODE_STR=y
CONFIG_ESPTOOLPY_FLASHMODE="dio"
CONFIG_ESPTOOLPY_FLASHFREQ_80M=y
# CONFIG_ESPTOOLPY_FLASHFREQ_40M is not set
# CONFIG_ESPTOOLPY_FLASHFREQ_26M is not set
# CONFIG_ESPTOOLPY_FLASHFREQ_20M is not set
CONFIG_ESPTOOLPY_FLASHFREQ="80m"
# CONFIG_ESPTOOLPY_FLASHSIZE_1MB is not set
# CONFIG_ESPTOOLPY_FLASHSIZE_2MB is not set
CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y
# CONFIG_ESPTOOLPY_FLASHSIZE_8MB is not set
# CONFIG_ESPTOOLPY_FLASHSIZE_16MB is not set
# CONFIG_ESPTOOLPY_FLASHSIZE_32MB is not set
# CONFIG_ESPTOOLPY_FLASHSIZE_64MB is not set
# CONFIG_ESPTOOLPY_FLASHSIZE_128MB is not set
CONFIG_ESPTOOLPY_FLASHSIZE="4MB"
# default:
# CONFIG_ESPTOOLPY_HEADER_FLASHSIZE_UPDATE is not set
CONFIG_ESPTOOLPY_BEFORE_RESET=y
# CONFIG_ESPTOOLPY_BEFORE_NORESET is not set
CONFIG_ESPTOOLPY_BEFORE="default_reset"
CONFIG_ESPTOOLPY_AFTER_RESET=y
# CONFIG_ESPTOOLPY_AFTER_NORESET is not set
CONFIG_ESPTOOLPY_AFTER="hard_reset"
CONFIG_ESPTOOLPY_MONITOR_BAUD=115200
# end of Serial flasher config

#
# Partition Table
#
# CONFIG_PARTITION_TABLE_SINGLE_APP is not set
# CONFIG_PARTITION_TABLE_SINGLE_APP_LARGE is not set
# CONFIG_PARTITION_TABLE_TWO_OTA is not set
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_OFFSET=0x8000
CONFIG_PARTITION_TABLE_MD5=y
# end of Partition Table

#
# Compiler options
#
# CONFIG_COMPILER_OPTIMIZATION_DEFAULT is not set
# CONFIG_COMPILER_OPTIMIZATION_SIZE is not set
CONFIG_COMPILER_OPTIMIZATION_PERF=y
# CONFIG_COMPILER_OPTIMIZATION_NONE is not set
# CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE is not set
# CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_SILENT is not set
CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE=y
CONFIG_COMPILER_FLOAT_LIB_FROM_GCCLIB=y
CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL=0
# default:
# CONFIG_COMPILER_OPTIMIZATION_CHECKS_SILENT is not set
CONFIG_COMPILER_HIDE_PATHS_MACROS=y
# default:
# CONFIG_COMPILER_CXX_EXCEPTIONS is not set
# default:
# CONFIG_COMPILER_CXX_RTTI is not set
CONFIG_COMPILER_STACK_CHECK_MODE_NONE=y
# CONFIG_COMPILER_STACK_CHECK_MODE_NORM is not set
# CONFIG_COMPILER_STACK_CHECK_MODE_STRONG is not set
# CONFIG_COMPILER_STACK_CHECK_MODE_ALL is not set
# default:
# CONFIG_COMPILER_WARN_WRITE_STRINGS is not set
# default:
# CONFIG_COMPILER_DISABLE_GCC12_WARNINGS is not set
# default:
# CONFIG_COMPILER_DUMP_RTL_FILES is not set
# end of Compiler options

#
# Component config
#

#
# Application Level Tracing
#
# CONFIG_APPTRACE_DEST_JTAG is not set
CONFIG_APPTRACE_DEST_NONE=y
# CONFIG_APPTRACE_DEST_UART1 is not set
# CONFIG_APPTRACE_DEST_UART2 is not set
CONFIG_APPTRACE_DEST_UART_NONE=y
CONFIG_APPTRACE_UART_TASK_PRIO=1
CONFIG_APPTRACE_LOCK_ENABLE=y
# end of Application Level Tracing

#
# Bluetooth
#
CONFIG_BT_ENABLED=y
CONFIG_BT_BLUEDROID_ENABLED=y
# CONFIG_BT_NIMBLE_ENABLED is not set
# CONFIG_BT_CONTROLLER_ONLY is not set
CONFIG_BT_CONTROLLER_ENABLED=y
# CONFIG_BT_CONTROLLER_DISABLED is not set

#
# Bluedroid Options
#
CONFIG_BT_BTC_TASK_STACK_SIZE=16384
CONFIG_BT_BLUEDROID_PINNED_TO_CORE_0=y
# CONFIG_BT_BLUEDROID_PINNED_TO_CORE_1 is not set
CONFIG_BT_BLUEDROID_PINNED_TO_CORE=0
CONFIG_BT_BTU_TASK_STACK_SIZE=8192
# default:
# CONFIG_BT_BLUEDROID_MEM_DEBUG is not set
CONFIG_BT_CLASSIC_ENABLED=y
# default:
# CONFIG_BT_CLASSIC_BQB_ENABLED is not set
CONFIG_BT_A2DP_ENABLE=y
CONFIG_BT_A2DP_APTX_DECODER=y
CONFIG_BT_A2DP_LDAC_DECODER=y
CONFIG_BT_A2DP_OPUS_DECODER=y
CONFIG_BT_A2DP_LC3PLUS_DECODER=y
CONFIG_BT_A2DP_AAC_DECODER=y
CONFIG_BT_SPP_ENABLED=y
# default:
# CONFIG_BT_L2CAP_ENABLED is not set
# default:
# CONFIG_BT_HFP_ENABLE is not set
# default:
# CONFIG_BT_HID_ENABLED is not set
CONFIG_BT_SSP_ENABLED=y
CONFIG_BT_BLE_ENABLED=y
CONFIG_BT_GATTS_ENABLE=y
# default:
# CONFIG_BT_GATTS_PPCP_CHAR_GAP is not set
# default:
# CONFIG_BT_BLE_BLUFI_ENABLE is not set
CONFIG_BT_GATT_MAX_SR_PROFILES=8
CONFIG_BT_GATT_MAX_SR_ATTRIBUTES=100
# CONFIG_BT_GATTS_SEND_SERVICE_CHANGE_MANUAL is not set
CONFIG_BT_GATTS_SEND_SERVICE_CHANGE_AUTO=y
CONFIG_BT_GATTS_SEND_SERVICE_CHANGE_MODE=0
# default:
# CONFIG_BT_GATTS_ROBUST_CACHING_ENABLED is not set
# default:
# CONFIG_BT_GATTS_DEVICE_NAME_WRITABLE is not set
# default:
# CONFIG_BT_GATTS_APPEARANCE_WRITABLE is not set
CONFIG_BT_GATTC_ENABLE=y
CONFIG_BT_GATTC_MAX_CACHE_CHAR=40
CONFIG_BT_GATTC_NOTIF_REG_MAX=5
# default:
# CONFIG_BT_GATTC_CACHE_NVS_FLASH is not set
CONFIG_BT_GATTC_CONNECT_RETRY_COUNT=3
CONFIG_BT_BLE_SMP_ENABLE=y
# default:
# CONFIG_BT_SMP_SLAVE_CON_PARAMS_UPD_ENABLE is not set
# default:
# CONFIG_BT_STACK_NO_LOG is not set

#
# BT DEBUG LOG LEVEL
#
# CONFIG_BT_LOG_HCI_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_HCI_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_HCI_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_HCI_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_HCI_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_HCI_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_HCI_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_HCI_TRACE_LEVEL=2
# CONFIG_BT_LOG_BTM_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_BTM_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_BTM_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_BTM_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_BTM_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_BTM_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_BTM_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_BTM_TRACE_LEVEL=2
# CONFIG_BT_LOG_L2CAP_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_L2CAP_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_L2CAP_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_L2CAP_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_L2CAP_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_L2CAP_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_L2CAP_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_L2CAP_TRACE_LEVEL=2
# CONFIG_BT_LOG_RFCOMM_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_RFCOMM_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_RFCOMM_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_RFCOMM_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_RFCOMM_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_RFCOMM_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_RFCOMM_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_RFCOMM_TRACE_LEVEL=2
# CONFIG_BT_LOG_SDP_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_SDP_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_SDP_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_SDP_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_SDP_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_SDP_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_SDP_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_SDP_TRACE_LEVEL=2
# CONFIG_BT_LOG_GAP_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_GAP_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_GAP_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_GAP_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_GAP_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_GAP_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_GAP_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_GAP_TRACE_LEVEL=2
# CONFIG_BT_LOG_BNEP_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_BNEP_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_BNEP_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_BNEP_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_BNEP_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_BNEP_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_BNEP_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_BNEP_TRACE_LEVEL=2
# CONFIG_BT_LOG_PAN_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_PAN_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_PAN_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_PAN_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_PAN_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_PAN_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_PAN_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_PAN_TRACE_LEVEL=2
# CONFIG_BT_LOG_A2D_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_A2D_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_A2D_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_A2D_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_A2D_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_A2D_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_A2D_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_A2D_TRACE_LEVEL=2
# CONFIG_BT_LOG_AVDT_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_AVDT_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_AVDT_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_AVDT_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_AVDT_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_AVDT_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_AVDT_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_AVDT_TRACE_LEVEL=2
# CONFIG_BT_LOG_AVCT_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_AVCT_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_AVCT_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_AVCT_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_AVCT_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_AVCT_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_AVCT_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_AVCT_TRACE_LEVEL=2
# CONFIG_BT_LOG_AVRC_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_AVRC_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_AVRC_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_AVRC_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_AVRC_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_AVRC_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_AVRC_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_AVRC_TRACE_LEVEL=2
# CONFIG_BT_LOG_MCA_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_MCA_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_MCA_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_MCA_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_MCA_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_MCA_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_MCA_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_MCA_TRACE_LEVEL=2
# CONFIG_BT_LOG_HID_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_HID_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_HID_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_HID_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_HID_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_HID_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_HID_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_HID_TRACE_LEVEL=2
# CONFIG_BT_LOG_APPL_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_APPL_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_APPL_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_APPL_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_APPL_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_APPL_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_APPL_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_APPL_TRACE_LEVEL=2
# CONFIG_BT_LOG_GATT_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_GATT_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_GATT_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_GATT_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_GATT_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_GATT_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_GATT_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_GATT_TRACE_LEVEL=2
# CONFIG_BT_LOG_SMP_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_SMP_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_SMP_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_SMP_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_SMP_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_SMP_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_SMP_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_SMP_TRACE_LEVEL=2
# CONFIG_BT_LOG_BTIF_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_BTIF_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_BTIF_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_BTIF_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_BTIF_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_BTIF_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_BTIF_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_BTIF_TRACE_LEVEL=2
# CONFIG_BT_LOG_BTC_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_BTC_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_BTC_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_BTC_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_BTC_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_BTC_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_BTC_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_BTC_TRACE_LEVEL=2
# CONFIG_BT_LOG_OSI_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_OSI_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_OSI_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_OSI_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_OSI_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_OSI_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_OSI_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_OSI_TRACE_LEVEL=2
# CONFIG_BT_LOG_BLUFI_TRACE_LEVEL_NONE is not set
# CONFIG_BT_LOG_BLUFI_TRACE_LEVEL_ERROR is not set
CONFIG_BT_LOG_BLUFI_TRACE_LEVEL_WARNING=y
# CONFIG_BT_LOG_BLUFI_TRACE_LEVEL_API is not set
# CONFIG_BT_LOG_BLUFI_TRACE_LEVEL_EVENT is not set
# CONFIG_BT_LOG_BLUFI_TRACE_LEVEL_DEBUG is not set
# CONFIG_BT_LOG_BLUFI_TRACE_LEVEL_VERBOSE is not set
CONFIG_BT_LOG_BLUFI_TRACE_LEVEL=2
# end of BT DEBUG LOG LEVEL

CONFIG_BT_ACL_CONNECTIONS=4
CONFIG_BT_MULTI_CONNECTION_ENBALE=y
# default:
# CONFIG_BT_ALLOCATION_FROM_SPIRAM_FIRST is not set
# default:
# CONFIG_BT_BLE_DYNAMIC_ENV_MEMORY is not set
# default:
# CONFIG_BT_BLE_HOST_QUEUE_CONG_CHECK is not set
CONFIG_BT_SMP_ENABLE=y
CONFIG_BT_SMP_MAX_BONDS=15
# default:
# CONFIG_BT_BLE_ACT_SCAN_REP_ADV_SCAN is not set
CONFIG_BT_BLE_ESTAB_LINK_CONN_TOUT=30
CONFIG_BT_MAX_DEVICE_NAME_LEN=32
# default:
# CONFIG_BT_BLE_RPA_SUPPORTED is not set
CONFIG_BT_BLE_RPA_TIMEOUT=900
# default:
# CONFIG_BT_BLE_HIGH_DUTY_ADV_INTERVAL is not set
# end of Bluedroid Options

#
# Controller Options
#
# CONFIG_BTDM_CTRL_MODE_BLE_ONLY is not set
# CONFIG_BTDM_CTRL_MODE_BR_EDR_ONLY is not set
CONFIG_BTDM_CTRL_MODE_BTDM=y
CONFIG_BTDM_CTRL_BLE_MAX_CONN=3
CONFIG_BTDM_CTRL_BR_EDR_MAX_ACL_CONN=2
CONFIG_BTDM_CTRL_BR_EDR_MAX_SYNC_CONN=0
# CONFIG_BTDM_CTRL_BR_EDR_SCO_DATA_PATH_HCI is not set
CONFIG_BTDM_CTRL_BR_EDR_SCO_DATA_PATH_PCM=y
CONFIG_BTDM_CTRL_BR_EDR_SCO_DATA_PATH_EFF=1
CONFIG_BTDM_CTRL_PCM_ROLE_EDGE_CONFIG=y
CONFIG_BTDM_CTRL_PCM_ROLE_MASTER=y
# CONFIG_BTDM_CTRL_PCM_ROLE_SLAVE is not set
CONFIG_BTDM_CTRL_PCM_POLAR_FALLING_EDGE=y
# CONFIG_BTDM_CTRL_PCM_POLAR_RISING_EDGE is not set
CONFIG_BTDM_CTRL_PCM_ROLE_EFF=0
CONFIG_BTDM_CTRL_PCM_POLAR_EFF=0
# default:
# CONFIG_BTDM_CTRL_AUTO_LATENCY is not set
CONFIG_BTDM_CTRL_LEGACY_AUTH_VENDOR_EVT=y
CONFIG_BTDM_CTRL_LEGACY_AUTH_VENDOR_EVT_EFF=y
CONFIG_BTDM_CTRL_BLE_MAX_CONN_EFF=3
CONFIG_BTDM_CTRL_BR_EDR_MAX_ACL_CONN_EFF=2
CONFIG_BTDM_CTRL_BR_EDR_MAX_SYNC_CONN_EFF=0
CONFIG_BTDM_CTRL_PINNED_TO_CORE_0=y
# CONFIG_BTDM_CTRL_PINNED_TO_CORE_1 is not set
CONFIG_BTDM_CTRL_PINNED_TO_CORE=0
CONFIG_BTDM_CTRL_HCI_MODE_VHCI=y
# CONFIG_BTDM_CTRL_HCI_MODE_UART_H4 is not set

#
# MODEM SLEEP Options
#
CONFIG_BTDM_CTRL_MODEM_SLEEP=y
CONFIG_BTDM_CTRL_MODEM_SLEEP_MODE_ORIG=y
# CONFIG_BTDM_CTRL_MODEM_SLEEP_MODE_EVED is not set
CONFIG_BTDM_CTRL_LPCLK_SEL_MAIN_XTAL=y
# end of MODEM SLEEP Options

CONFIG_BTDM_BLE_DEFAULT_SCA_250PPM=y
CONFIG_BTDM_BLE_SLEEP_CLOCK_ACCURACY_INDEX_EFF=1
CONFIG_BTDM_BLE_SCAN_DUPL=y
CONFIG_BTDM_SCAN_DUPL_TYPE_DEVICE=y
# CONFIG_BTDM_SCAN_DUPL_TYPE_DATA is not set
# CONFIG_BTDM_SCAN_DUPL_TYPE_DATA_DEVICE is not set
CONFIG_BTDM_SCAN_DUPL_TYPE=0
CONFIG_BTDM_SCAN_DUPL_CACHE_SIZE=100
CONFIG_BTDM_SCAN_DUPL_CACHE_REFRESH_PERIOD=0
# default:
# CONFIG_BTDM_BLE_MESH_SCAN_DUPL_EN is not set
CONFIG_BTDM_CTRL_FULL_SCAN_SUPPORTED=y
CONFIG_BTDM_BLE_ADV_REPORT_FLOW_CTRL_SUPP=y
CONFIG_BTDM_BLE_ADV_REPORT_FLOW_CTRL_NUM=100
CONFIG_BTDM_BLE_ADV_REPORT_DISCARD_THRSHOLD=20
CONFIG_BTDM_RESERVE_DRAM=0xdb5c
CONFIG_BTDM_CTRL_HLI=y
# end of Controller Options
# end of Bluetooth

# default:
# CONFIG_BLE_MESH is not set

#
# Driver Configurations
#

#
# Legacy ADC Configuration
#
CONFIG_ADC_DISABLE_DAC=y
# default:
# CONFIG_ADC_SUPPRESS_DEPRECATE_WARN is not set

#
# Legacy ADC Calibration Configuration
#
CONFIG_ADC_CAL_EFUSE_TP_ENABLE=y
CONFIG_ADC_CAL_EFUSE_VREF_ENABLE=y
CONFIG_ADC_CAL_LUT_ENABLE=y
# default:
# CONFIG_ADC_CALI_SUPPRESS_DEPRECATE_WARN is not set
# end of Legacy ADC Calibration Configuration
# end of Legacy ADC Configuration

#
# SPI Configuration
#
# default:
# CONFIG_SPI_MASTER_IN_IRAM is not set
CONFIG_SPI_MASTER_ISR_IN_IRAM=y
# default:
# CONFIG_SPI_SLAVE_IN_IRAM is not set
CONFIG_SPI_SLAVE_ISR_IN_IRAM=y
# end of SPI Configuration

#
# TWAI Configuration
#
# default:
# CONFIG_TWAI_ISR_IN_IRAM is not set
# default:
# CONFIG_TWAI_ERRATA_FIX_BUS_OFF_REC is not set
# default:
# CONFIG_TWAI_ERRATA_FIX_TX_INTR_LOST is not set
# default:
# CONFIG_TWAI_ERRATA_FIX_RX_FRAME_INVALID is not set
# default:
# CONFIG_TWAI_ERRATA_FIX_RX_FIFO_CORRUPT is not set
CONFIG_TWAI_ERRATA_FIX_LISTEN_ONLY_DOM=y
# end of TWAI Configuration

#
# UART Configuration
#
CONFIG_UART_ISR_IN_IRAM=y
# end of UART Configuration

#
# GPIO Configuration
#
# default:
# CONFIG_GPIO_ESP32_SUPPORT_SWITCH_SLP_PULL is not set
# default:
# CONFIG_GPIO_CTRL_FUNC_IN_IRAM is not set
# end of GPIO Configuration

#
# Sigma Delta Modulator Configuration
#
# default:
# CONFIG_SDM_CTRL_FUNC_IN_IRAM is not set
# default:
# CONFIG_SDM_SUPPRESS_DEPRECATE_WARN is not set
# default:
# CONFIG_SDM_ENABLE_DEBUG_LOG is not set
# end of Sigma Delta Modulator Configuration

#
# GPTimer Configuration
#
CONFIG_GPTIMER_ISR_HANDLER_IN_IRAM=y
# default:
# CONFIG_GPTIMER_CTRL_FUNC_IN_IRAM is not set
# default:
# CONFIG_GPTIMER_ISR_IRAM_SAFE is not set
# default:
# CONFIG_GPTIMER_SUPPRESS_DEPRECATE_WARN is not set
# default:
# CONFIG_GPTIMER_ENABLE_DEBUG_LOG is not set
# end of GPTimer Configuration

#
# PCNT Configuration
#
# default:
# CONFIG_PCNT_CTRL_FUNC_IN_IRAM is not set
# default:
# CONFIG_PCNT_ISR_IRAM_SAFE is not set
# default:
# CONFIG_PCNT_SUPPRESS_DEPRECATE_WARN is not set
# default:
# CONFIG_PCNT_ENABLE_DEBUG_LOG is not set
# end of PCNT Configuration

#
# RMT Configuration
#
# default:
# CONFIG_RMT_ISR_IRAM_SAFE is not set
# default:
# CONFIG_RMT_RECV_FUNC_IN_IRAM is not set
# default:
# CONFIG_RMT_SUPPRESS_DEPRECATE_WARN is not set
# default:
# CONFIG_RMT_ENABLE_DEBUG_LOG is not set
# end of RMT Configuration

#
# MCPWM Configuration
#
# default:
# CONFIG_MCPWM_ISR_IRAM_SAFE is not set
# default:
# CONFIG_MCPWM_CTRL_FUNC_IN_IRAM is not set
# default:
# CONFIG_MCPWM_SUPPRESS_DEPRECATE_WARN is not set
# default:
# CONFIG_MCPWM_ENABLE_DEBUG_LOG is not set
# end of MCPWM Configuration

#
# I2S Configuration
#
# default:
# CONFIG_I2S_ISR_IRAM_SAFE is not set
# default:
# CONFIG_I2S_SUPPRESS_DEPRECATE_WARN is not set
# default:
# CONFIG_I2S_ENABLE_DEBUG_LOG is not set
# end of I2S Configuration

#
# DAC Configuration
#
# default:
# CONFIG_DAC_CTRL_FUNC_IN_IRAM is not set
# default:
# CONFIG_DAC_ISR_IRAM_SAFE is not set
# default:
# CONFIG_DAC_SUPPRESS_DEPRECATE_WARN is not set
# default:
# CONFIG_DAC_ENABLE_DEBUG_LOG is not set
CONFIG_DAC_DMA_AUTO_16BIT_ALIGN=y
# end of DAC Configuration
# end of Driver Configurations

#
# eFuse Bit Manager
#
# default:
# CONFIG_EFUSE_CUSTOM_TABLE is not set
# default:
# CONFIG_EFUSE_VIRTUAL is not set
# CONFIG_EFUSE_CODE_SCHEME_COMPAT_NONE is not set
CONFIG_EFUSE_CODE_SCHEME_COMPAT_3_4=y
# CONFIG_EFUSE_CODE_SCHEME_COMPAT_REPEAT is not set
CONFIG_EFUSE_MAX_BLK_LEN=192
# end of eFuse Bit Manager

#
# ESP-TLS
#
CONFIG_ESP_TLS_USING_MBEDTLS=y
# default:
# CONFIG_ESP_TLS_USE_SECURE_ELEMENT is not set
# default:
# CONFIG_ESP_TLS_CLIENT_SESSION_TICKETS is not set
# default:
# CONFIG_ESP_TLS_SERVER is not set
# default:
# CONFIG_ESP_TLS_PSK_VERIFICATION is not set
# default:
# CONFIG_ESP_TLS_INSECURE is not set
# end of ESP-TLS

#
# ADC and ADC Calibration
#
# default:
# CONFIG_ADC_ONESHOT_CTRL_FUNC_IN_IRAM is not set
# default:
# CONFIG_ADC_CONTINUOUS_ISR_IRAM_SAFE is not set

#
# ADC Calibration Configurations
#
CONFIG_ADC_CALI_EFUSE_TP_ENABLE=y
CONFIG_ADC_CALI_EFUSE_VREF_ENABLE=y
CONFIG_ADC_CALI_LUT_ENABLE=y
# end of ADC Calibration Configurations

CONFIG_ADC_DISABLE_DAC_OUTPUT=y
# end of ADC and ADC Calibration

#
# Wireless Coexistence
#
CONFIG_ESP_COEX_SW_COEXIST_ENABLE=y
# end of Wireless Coexistence

#
# Common ESP-related
#
CONFIG_ESP_ERR_TO_NAME_LOOKUP=y
# end of Common ESP-related

#
# Ethernet
#
CONFIG_ETH_ENABLED=y
CONFIG_ETH_USE_ESP32_EMAC=y
CONFIG_ETH_PHY_INTERFACE_RMII=y
CONFIG_ETH_RMII_CLK_INPUT=y
# CONFIG_ETH_RMII_CLK_OUTPUT is not set
CONFIG_ETH_RMII_CLK_IN_GPIO=0
CONFIG_ETH_DMA_BUFFER_SIZE=512
CONFIG_ETH_DMA_RX_BUFFER_NUM=10
CONFIG_ETH_DMA_TX_BUFFER_NUM=10
# default:
# CONFIG_ETH_IRAM_OPTIMIZATION is not set
CONFIG_ETH_USE_SPI_ETHERNET=y
# default:
# CONFIG_ETH_SPI_ETHERNET_DM9051 is not set
# default:
# CONFIG_ETH_SPI_ETHERNET_W5500 is not set
# default:
# CONFIG_ETH_SPI_ETHERNET_KSZ8851SNL is not set
# default:
# CONFIG_ETH_USE_OPENETH is not set
# default:
# CONFIG_ETH_TRANSMIT_MUTEX is not set
# end of Ethernet

#
# Event Loop Library
#
# default:
# CONFIG_ESP_EVENT_LOOP_PROFILING is not set
CONFIG_ESP_EVENT_POST_FROM_ISR=y
CONFIG_ESP_EVENT_POST_FROM_IRAM_ISR=y
# end of Event Loop Library

#
# GDB Stub
#
# end of GDB Stub

#
# ESP HTTP client
#
CONFIG_ESP_HTTP_CLIENT_ENABLE_HTTPS=y
# default:
# CONFIG_ESP_HTTP_CLIENT_ENABLE_BASIC_AUTH is not set
CONFIG_ESP_HTTP_CLIENT_ENABLE_DIGEST_AUTH=y
# end of ESP HTTP client

#
# HTTP Server
#
CONFIG_HTTPD_MAX_REQ_HDR_LEN=512
CONFIG_HTTPD_MAX_URI_LEN=512
CONFIG_HTTPD_ERR_RESP_NO_DELAY=y
CONFIG_HTTPD_PURGE_BUF_LEN=32
# default:
# CONFIG_HTTPD_LOG_PURGE_DATA is not set
# default:
# CONFIG_HTTPD_WS_SUPPORT is not set
# default:
# CONFIG_HTTPD_QUEUE_WORK_BLOCKING is not set
# end of HTTP Server

#
# ESP HTTPS OTA
#
# default:
# CONFIG_ESP_HTTPS_OTA_DECRYPT_CB is not set
# default:
# CONFIG_ESP_HTTPS_OTA_ALLOW_HTTP is not set
# end of ESP HTTPS OTA

#
# ESP HTTPS server
#
# default:
# CONFIG_ESP_HTTPS_SERVER_ENABLE is not set
# end of ESP HTTPS server

#
# Hardware Settings
#

#
# Chip revision
#
# CONFIG_ESP32_REV_MIN_0 is not set
CONFIG_ESP32_REV_MIN_1=y
# CONFIG_ESP32_REV_MIN_1_1 is not set
# CONFIG_ESP32_REV_MIN_2 is not set
# CONFIG_ESP32_REV_MIN_3 is not set
# CONFIG_ESP32_REV_MIN_3_1 is not set
CONFIG_ESP32_REV_MIN=1
CONFIG_ESP32_REV_MIN_FULL=100
CONFIG_ESP_REV_MIN_FULL=100

#
# Maximum Supported ESP32 Revision (Rev v3.99)
#
CONFIG_ESP32_REV_MAX_FULL=399
CONFIG_ESP_REV_MAX_FULL=399
# end of Chip revision

#
# MAC Config
#
CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_STA=y
CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_AP=y
CONFIG_ESP_MAC_ADDR_UNIVERSE_BT=y
CONFIG_ESP_MAC_ADDR_UNIVERSE_ETH=y
CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES_FOUR=y
# CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES_TWO is not set
CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES_FOUR=y
CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES=4
# default:
# CONFIG_ESP_MAC_IGNORE_MAC_CRC_ERROR is not set
# default:
# CONFIG_ESP_MAC_USE_CUSTOM_MAC_AS_BASE_MAC is not set
# end of MAC Config

#
# Sleep Config
#
CONFIG_ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND=y
CONFIG_ESP_SLEEP_PSRAM_LEAKAGE_WORKAROUND=y
# default:
# CONFIG_ESP_SLEEP_MSPI_NEED_ALL_IO_PU is not set
CONFIG_ESP_SLEEP_RTC_BUS_ISO_WORKAROUND=y
# default:
# CONFIG_ESP_SLEEP_GPIO_RESET_WORKAROUND is not set
CONFIG_ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY=2000
# default:
# CONFIG_ESP_SLEEP_DEBUG is not set
CONFIG_ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS=y
# default:
# CONFIG_ESP_SLEEP_CACHE_SAFE_ASSERTION is not set
# end of Sleep Config

#
# RTC Clock Config
#
CONFIG_RTC_CLK_SRC_INT_RC=y
# CONFIG_RTC_CLK_SRC_EXT_CRYS is not set
# CONFIG_RTC_CLK_SRC_EXT_OSC is not set
# CONFIG_RTC_CLK_SRC_INT_8MD256 is not set
CONFIG_RTC_CLK_CAL_CYCLES=1024
# end of RTC Clock Config

#
# Peripheral Control
#
CONFIG_PERIPH_CTRL_FUNC_IN_IRAM=y
# end of Peripheral Control

#
# Main XTAL Config
#
# CONFIG_XTAL_FREQ_26 is not set
CONFIG_XTAL_FREQ_40=y
# CONFIG_XTAL_FREQ_AUTO is not set
CONFIG_XTAL_FREQ=40
# end of Main XTAL Config
# end of Hardware Settings

#
# LCD and Touch Panel
#

#
# LCD Touch Drivers are maintained in the IDF Component Registry
#

#
# LCD Peripheral Configuration
#
CONFIG_LCD_PANEL_IO_FORMAT_BUF_SIZE=32
# default:
# CONFIG_LCD_ENABLE_DEBUG_LOG is not set
# end of LCD Peripheral Configuration
# end of LCD and Touch Panel

#
# ESP NETIF Adapter
#
CONFIG_ESP_NETIF_IP_LOST_TIMER_INTERVAL=120
CONFIG_ESP_NETIF_TCPIP_LWIP=y
# CONFIG_ESP_NETIF_LOOPBACK is not set
CONFIG_ESP_NETIF_USES_TCPIP_WITH_BSD_API=y
# default:
# CONFIG_ESP_NETIF_RECEIVE_REPORT_ERRORS is not set
# default:
# CONFIG_ESP_NETIF_L2_TAP is not set
# default:
# CONFIG_ESP_NETIF_BRIDGE_EN is not set
# end of ESP NETIF Adapter

#
# Partition API Configuration
#
# end of Partition API Configuration

#
# PHY
#
CONFIG_ESP_PHY_CALIBRATION_AND_DATA_STORAGE=y
# default:
# CONFIG_ESP_PHY_INIT_DATA_IN_PARTITION is not set
CONFIG_ESP_PHY_MAX_WIFI_TX_POWER=20
CONFIG_ESP_PHY_MAX_TX_POWER=20
CONFIG_ESP_PHY_REDUCE_TX_POWER=y
CONFIG_ESP_PHY_RF_CAL_PARTIAL=y
# CONFIG_ESP_PHY_RF_CAL_NONE is not set
# CONFIG_ESP_PHY_RF_CAL_FULL is not set
CONFIG_ESP_PHY_CALIBRATION_MODE=0
# default:
# CONFIG_ESP_PHY_PLL_TRACK_DEBUG is not set
# end of PHY

#
# Power Management
#
CONFIG_PM_ENABLE=y
# default:
# CONFIG_PM_DFS_INIT_AUTO is not set
# default:
# CONFIG_PM_PROFILING is not set
# default:
# CONFIG_PM_TRACE is not set
CONFIG_PM_UPDATE_CCOMPARE_HLI_WORKAROUND=y
# end of Power Management

#
# ESP PSRAM
#
CONFIG_SPIRAM=y

#
# SPI RAM config
#
CONFIG_SPIRAM_MODE_QUAD=y
CONFIG_SPIRAM_TYPE_AUTO=y
# CONFIG_SPIRAM_TYPE_ESPPSRAM16 is not set
# CONFIG_SPIRAM_TYPE_ESPPSRAM32 is not set
# CONFIG_SPIRAM_TYPE_ESPPSRAM64 is not set
# CONFIG_SPIRAM_SPEED_40M is not set
CONFIG_SPIRAM_SPEED_80M=y
CONFIG_SPIRAM_SPEED=80
CONFIG_SPIRAM_BOOT_INIT=y
# default:
# CONFIG_SPIRAM_IGNORE_NOTFOUND is not set
# CONFIG_SPIRAM_USE_MEMMAP is not set
CONFIG_SPIRAM_USE_CAPS_ALLOC=y
# CONFIG_SPIRAM_USE_MALLOC is not set
CONFIG_SPIRAM_MEMTEST=y
# default:
# CONFIG_SPIRAM_TRY_ALLOCATE_WIFI_LWIP is not set
# default:
# CONFIG_SPIRAM_ALLOW_BSS_SEG_EXTERNAL_MEMORY is not set
# default:
# CONFIG_SPIRAM_ALLOW_NOINIT_SEG_EXTERNAL_MEMORY is not set
CONFIG_SPIRAM_CACHE_WORKAROUND=y

#
# SPIRAM cache workaround debugging
#
CONFIG_SPIRAM_CACHE_WORKAROUND_STRATEGY_MEMW=y
# CONFIG_SPIRAM_CACHE_WORKAROUND_STRATEGY_DUPLDST is not set
# CONFIG_SPIRAM_CACHE_WORKAROUND_STRATEGY_NOPS is not set
# end of SPIRAM cache workaround debugging

#
# SPIRAM workaround libraries placement
#
CONFIG_SPIRAM_CACHE_LIBJMP_IN_IRAM=y
CONFIG_SPIRAM_CACHE_LIBMATH_IN_IRAM=y
CONFIG_SPIRAM_CACHE_LIBNUMPARSER_IN_IRAM=y
CONFIG_SPIRAM_CACHE_LIBIO_IN_IRAM=y
CONFIG_SPIRAM_CACHE_LIBTIME_IN_IRAM=y
CONFIG_SPIRAM_CACHE_LIBCHAR_IN_IRAM=y
CONFIG_SPIRAM_CACHE_LIBMEM_IN_IRAM=y
CONFIG_SPIRAM_CACHE_LIBSTR_IN_IRAM=y
CONFIG_SPIRAM_CACHE_LIBRAND_IN_IRAM=y
CONFIG_SPIRAM_CACHE_LIBENV_IN_IRAM=y
CONFIG_SPIRAM_CACHE_LIBFILE_IN_IRAM=y
CONFIG_SPIRAM_CACHE_LIBMISC_IN_IRAM=y
# end of SPIRAM workaround libraries placement

CONFIG_SPIRAM_BANKSWITCH_ENABLE=y
CONFIG_SPIRAM_BANKSWITCH_RESERVE=8
# CONFIG_SPIRAM_OCCUPY_HSPI_HOST is not set
CONFIG_SPIRAM_OCCUPY_VSPI_HOST=y
# CONFIG_SPIRAM_OCCUPY_NO_HOST is not set

#
# PSRAM clock and cs IO for ESP32-DOWD
#
CONFIG_D0WD_PSRAM_CLK_IO=17
CONFIG_D0WD_PSRAM_CS_IO=16
# end of PSRAM clock and cs IO for ESP32-DOWD

#
# PSRAM clock and cs IO for ESP32-D2WD
#
CONFIG_D2WD_PSRAM_CLK_IO=9
CONFIG_D2WD_PSRAM_CS_IO=10
# end of PSRAM clock and cs IO for ESP32-D2WD

#
# PSRAM clock and cs IO for ESP32-PICO-D4
#
CONFIG_PICO_PSRAM_CS_IO=10
# end of PSRAM clock and cs IO for ESP32-PICO-D4

# default:
# CONFIG_SPIRAM_CUSTOM_SPIWP_SD3_PIN is not set
CONFIG_SPIRAM_SPIWP_SD3_PIN=7
# default:
# CONFIG_SPIRAM_2T_MODE is not set
# end of SPI RAM config
# end of ESP PSRAM

#
# ESP Ringbuf
#
# default:
# CONFIG_RINGBUF_PLACE_FUNCTIONS_INTO_FLASH is not set
# end of ESP Ringbuf

#
# ESP System Settings
#
# CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_80 is not set
# CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_160 is not set
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240=y
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ=240

#
# Memory
#
# default:
# CONFIG_ESP32_USE_FIXED_STATIC_RAM_SIZE is not set

#
# Non-backward compatible options
#
# default:
# CONFIG_ESP_SYSTEM_ESP32_SRAM1_REGION_AS_IRAM is not set
# end of Non-backward compatible options
# end of Memory

#
# Trace memory
#
# default:
# CONFIG_ESP32_TRAX is not set
CONFIG_ESP32_TRACEMEM_RESERVE_DRAM=0x0
# end of Trace memory

# CONFIG_ESP_SYSTEM_PANIC_PRINT_HALT is not set
CONFIG_ESP_SYSTEM_PANIC_PRINT_REBOOT=y
# CONFIG_ESP_SYSTEM_PANIC_SILENT_REBOOT is not set
# CONFIG_ESP_SYSTEM_PANIC_GDBSTUB is not set
# CONFIG_ESP_SYSTEM_GDBSTUB_RUNTIME is not set
CONFIG_ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS=0

#
# Memory protection
#
# end of Memory protection

CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE=32
CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE=4096
CONFIG_ESP_MAIN_TASK_STACK_SIZE=8192
CONFIG_ESP_MAIN_TASK_AFFINITY_CPU0=y
# CONFIG_ESP_MAIN_TASK_AFFINITY_CPU1 is not set
# CONFIG_ESP_MAIN_TASK_AFFINITY_NO_AFFINITY is not set
CONFIG_ESP_MAIN_TASK_AFFINITY=0x0
CONFIG_ESP_MINIMAL_SHARED_STACK_SIZE=2048
CONFIG_ESP_CONSOLE_UART_DEFAULT=y
# CONFIG_ESP_CONSOLE_UART_CUSTOM is not set
# CONFIG_ESP_CONSOLE_NONE is not set
CONFIG_ESP_CONSOLE_UART=y
CONFIG_ESP_CONSOLE_MULTIPLE_UART=y
CONFIG_ESP_CONSOLE_UART_NUM=0
CONFIG_ESP_CONSOLE_UART_BAUDRATE=115200
CONFIG_ESP_INT_WDT=y
CONFIG_ESP_INT_WDT_TIMEOUT_MS=300
CONFIG_ESP_INT_WDT_CHECK_CPU1=y
CONFIG_ESP_TASK_WDT_EN=y
CONFIG_ESP_TASK_WDT_INIT=y
# default:
# CONFIG_ESP_TASK_WDT_PANIC is not set
CONFIG_ESP_TASK_WDT_TIMEOUT_S=5
CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0=y
CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1=y
# default:
# CONFIG_ESP_PANIC_HANDLER_IRAM is not set
# default:
# CONFIG_ESP_DEBUG_STUBS_ENABLE is not set
CONFIG_ESP_DEBUG_OCDAWARE=y
CONFIG_ESP_SYSTEM_CHECK_INT_LEVEL_5=y

#
# Brownout Detector
#
CONFIG_ESP_BROWNOUT_DET=y
CONFIG_ESP_BROWNOUT_DET_LVL_SEL_0=y
# CONFIG_ESP_BROWNOUT_DET_LVL_SEL_1 is not set
# CONFIG_ESP_BROWNOUT_DET_LVL_SEL_2 is not set
# CONFIG_ESP_BROWNOUT_DET_LVL_SEL_3 is not set
# CONFIG_ESP_BROWNOUT_DET_LVL_SEL_4 is not set
# CONFIG_ESP_BROWNOUT_DET_LVL_SEL_5 is not set
# CONFIG_ESP_BROWNOUT_DET_LVL_SEL_6 is not set
# CONFIG_ESP_BROWNOUT_DET_LVL_SEL_7 is not set
CONFIG_ESP_BROWNOUT_DET_LVL=0
# end of Brownout Detector

# default:
# CONFIG_ESP32_DISABLE_BASIC_ROM_CONSOLE is not set
CONFIG_ESP32_ECO3_CACHE_LOCK_FIX=y
CONFIG_ESP_SYSTEM_BROWNOUT_INTR=y
# end of ESP System Settings

#
# IPC (Inter-Processor Call)
#
CONFIG_ESP_IPC_TASK_STACK_SIZE=1536
CONFIG_ESP_IPC_USES_CALLERS_PRIORITY=y
CONFIG_ESP_IPC_ISR_ENABLE=y
# end of IPC (Inter-Processor Call)

#
# High resolution timer (esp_timer)
#
# default:
# CONFIG_ESP_TIMER_PROFILING is not set
CONFIG_ESP_TIME_FUNCS_USE_RTC_TIMER=y
CONFIG_ESP_TIME_FUNCS_USE_ESP_TIMER=y
CONFIG_ESP_TIMER_TASK_STACK_SIZE=3584
CONFIG_ESP_TIMER_INTERRUPT_LEVEL=1
# default:
# CONFIG_ESP_TIMER_SHOW_EXPERIMENTAL is not set
CONFIG_ESP_TIMER_TASK_AFFINITY=0x0
CONFIG_ESP_TIMER_TASK_AFFINITY_CPU0=y
CONFIG_ESP_TIMER_ISR_AFFINITY=0x1
CONFIG_ESP_TIMER_ISR_AFFINITY_CPU0=y
# default:
# CONFIG_ESP_TIMER_SUPPORTS_ISR_DISPATCH_METHOD is not set
CONFIG_ESP_TIMER_IMPL_TG0_LAC=y
# end of High resolution timer (esp_timer)

#
# Wi-Fi
#
CONFIG_ESP_WIFI_ENABLED=y
CONFIG_ESP_WIFI_STATIC_RX_BUFFER_NUM=10
CONFIG_ESP_WIFI_DYNAMIC_RX_BUFFER_NUM=32
CONFIG_ESP_WIFI_STATIC_TX_BUFFER=y
# CONFIG_ESP_WIFI_DYNAMIC_TX_BUFFER is not set
CONFIG_ESP_WIFI_TX_BUFFER_TYPE=0
CONFIG_ESP_WIFI_STATIC_TX_BUFFER_NUM=16
CONFIG_ESP_WIFI_CACHE_TX_BUFFER_NUM=32
CONFIG_ESP_WIFI_STATIC_RX_MGMT_BUFFER=y
# CONFIG_ESP_WIFI_DYNAMIC_RX_MGMT_BUFFER is not set
CONFIG_ESP_WIFI_DYNAMIC_RX_MGMT_BUF=0
CONFIG_ESP_WIFI_RX_MGMT_BUF_NUM_DEF=5
# default:
# CONFIG_ESP_WIFI_CSI_ENABLED is not set
CONFIG_ESP_WIFI_AMPDU_TX_ENABLED=y
CONFIG_ESP_WIFI_TX_BA_WIN=6
CONFIG_ESP_WIFI_AMPDU_RX_ENABLED=y
CONFIG_ESP_WIFI_RX_BA_WIN=6
# default:
# CONFIG_ESP_WIFI_AMSDU_TX_ENABLED is not set
CONFIG_ESP_WIFI_NVS_ENABLED=y
CONFIG_ESP_WIFI_TASK_PINNED_TO_CORE_0=y
# CONFIG_ESP_WIFI_TASK_PINNED_TO_CORE_1 is not set
CONFIG_ESP_WIFI_SOFTAP_BEACON_MAX_LEN=752
CONFIG_ESP_WIFI_MGMT_SBUF_NUM=32
CONFIG_ESP_WIFI_IRAM_OPT=y
# default:
# CONFIG_ESP_WIFI_EXTRA_IRAM_OPT is not set
CONFIG_ESP_WIFI_RX_IRAM_OPT=y
CONFIG_ESP_WIFI_ENABLE_WPA3_SAE=y
CONFIG_ESP_WIFI_ENABLE_SAE_PK=y
CONFIG_ESP_WIFI_SOFTAP_SAE_SUPPORT=y
CONFIG_ESP_WIFI_ENABLE_WPA3_OWE_STA=y
# default:
# CONFIG_ESP_WIFI_SLP_IRAM_OPT is not set
CONFIG_ESP_WIFI_STA_DISCONNECTED_PM_ENABLE=y
# default:
# CONFIG_ESP_WIFI_GMAC_SUPPORT is not set
CONFIG_ESP_WIFI_SOFTAP_SUPPORT=y
# default:
# CONFIG_ESP_WIFI_SLP_BEACON_LOST_OPT is not set
CONFIG_ESP_WIFI_ESPNOW_MAX_ENCRYPT_NUM=7
# default:
# CONFIG_ESP_WIFI_NAN_ENABLE is not set
CONFIG_ESP_WIFI_MBEDTLS_CRYPTO=y
CONFIG_ESP_WIFI_MBEDTLS_TLS_CLIENT=y
# default:
# CONFIG_ESP_WIFI_WAPI_PSK is not set
# default:
# CONFIG_ESP_WIFI_11KV_SUPPORT is not set
# default:
# CONFIG_ESP_WIFI_MBO_SUPPORT is not set
# default:
# CONFIG_ESP_WIFI_DPP_SUPPORT is not set
# default:
# CONFIG_ESP_WIFI_11R_SUPPORT is not set
# default:
# CONFIG_ESP_WIFI_WPS_SOFTAP_REGISTRAR is not set

#
# WPS Configuration Options
#
# default:
# CONFIG_ESP_WIFI_WPS_STRICT is not set
# default:
# CONFIG_ESP_WIFI_WPS_PASSPHRASE is not set
# end of WPS Configuration Options

# default:
# CONFIG_ESP_WIFI_DEBUG_PRINT is not set
# default:
# CONFIG_ESP_WIFI_TESTING_OPTIONS is not set
CONFIG_ESP_WIFI_ENTERPRISE_SUPPORT=y
# default:
# CONFIG_ESP_WIFI_ENT_FREE_DYNAMIC_BUFFER is not set
# end of Wi-Fi

#
# Core dump
#
# CONFIG_ESP_COREDUMP_ENABLE_TO_FLASH is not set
# CONFIG_ESP_COREDUMP_ENABLE_TO_UART is not set
CONFIG_ESP_COREDUMP_ENABLE_TO_NONE=y
# end of Core dump

#
# FAT Filesystem support
#
CONFIG_FATFS_VOLUME_COUNT=2
CONFIG_FATFS_LFN_NONE=y
# CONFIG_FATFS_LFN_HEAP is not set
# CONFIG_FATFS_LFN_STACK is not set
# CONFIG_FATFS_SECTOR_512 is not set
CONFIG_FATFS_SECTOR_4096=y
# CONFIG_FATFS_CODEPAGE_DYNAMIC is not set
CONFIG_FATFS_CODEPAGE_437=y
# CONFIG_FATFS_CODEPAGE_720 is not set
# CONFIG_FATFS_CODEPAGE_737 is not set
# CONFIG_FATFS_CODEPAGE_771 is not set
# CONFIG_FATFS_CODEPAGE_775 is not set
# CONFIG_FATFS_CODEPAGE_850 is not set
# CONFIG_FATFS_CODEPAGE_852 is not set
# CONFIG_FATFS_CODEPAGE_855 is not set
# CONFIG_FATFS_CODEPAGE_857 is not set
# CONFIG_FATFS_CODEPAGE_860 is not set
# CONFIG_FATFS_CODEPAGE_861 is not set
# CONFIG_FATFS_CODEPAGE_862 is not set
# CONFIG_FATFS_CODEPAGE_863 is not set
# CONFIG_FATFS_CODEPAGE_864 is not set
# CONFIG_FATFS_CODEPAGE_865 is not set
# CONFIG_FATFS_CODEPAGE_866 is not set
# CONFIG_FATFS_CODEPAGE_869 is not set
# CONFIG_FATFS_CODEPAGE_932 is not set
# CONFIG_FATFS_CODEPAGE_936 is not set
# CONFIG_FATFS_CODEPAGE_949 is not set
# CONFIG_FATFS_CODEPAGE_950 is not set
CONFIG_FATFS_CODEPAGE=437
CONFIG_FATFS_FS_LOCK=0
CONFIG_FATFS_TIMEOUT_MS=10000
CONFIG_FATFS_PER_FILE_CACHE=y
CONFIG_FATFS_ALLOC_PREFER_EXTRAM=y
# default:
# CONFIG_FATFS_USE_FASTSEEK is not set
CONFIG_FATFS_VFS_FSTAT_BLKSIZE=0
# end of FAT Filesystem support

#
# FreeRTOS
#

#
# Kernel
#
# default:
# CONFIG_FREERTOS_SMP is not set
# default:
# CONFIG_FREERTOS_UNICORE is not set
CONFIG_FREERTOS_HZ=100
# CONFIG_FREERTOS_CHECK_STACKOVERFLOW_NONE is not set
# CONFIG_FREERTOS_CHECK_STACKOVERFLOW_PTRVAL is not set
CONFIG_FREERTOS_CHECK_STACKOVERFLOW_CANARY=y
CONFIG_FREERTOS_THREAD_LOCAL_STORAGE_POINTERS=1
CONFIG_FREERTOS_IDLE_TASK_STACKSIZE=1536
# default:
# CONFIG_FREERTOS_USE_IDLE_HOOK is not set
# default:
# CONFIG_FREERTOS_USE_TICK_HOOK is not set
CONFIG_FREERTOS_MAX_TASK_NAME_LEN=16
CONFIG_FREERTOS_ENABLE_BACKWARD_COMPATIBILITY=y
CONFIG_FREERTOS_TIMER_TASK_PRIORITY=1
CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH=2048
CONFIG_FREERTOS_TIMER_QUEUE_LENGTH=10
CONFIG_FREERTOS_QUEUE_REGISTRY_SIZE=0
CONFIG_FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES=1
# default:
# CONFIG_FREERTOS_USE_TRACE_FACILITY is not set
# default:
# CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS is not set
# default:
# CONFIG_FREERTOS_USE_TICKLESS_IDLE is not set
# end of Kernel

#
# Port
#
# default:
# CONFIG_FREERTOS_WATCHPOINT_END_OF_STACK is not set
CONFIG_FREERTOS_TLSP_DELETION_CALLBACKS=y
# default:
# CONFIG_FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP is not set
CONFIG_FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER=y
CONFIG_FREERTOS_ISR_STACKSIZE=1536
CONFIG_FREERTOS_INTERRUPT_BACKTRACE=y
# default:
# CONFIG_FREERTOS_FPU_IN_ISR is not set
CONFIG_FREERTOS_TICK_SUPPORT_CORETIMER=y
CONFIG_FREERTOS_CORETIMER_0=y
# CONFIG_FREERTOS_CORETIMER_1 is not set
CONFIG_FREERTOS_SYSTICK_USES_CCOUNT=y
# default:
# CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH is not set
CONFIG_FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH=y
# default:
# CONFIG_FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE is not set
CONFIG_FREERTOS_ENABLE_TASK_SNAPSHOT=y
# end of Port

CONFIG_FREERTOS_NO_AFFINITY=0x7FFFFFFF
CONFIG_FREERTOS_SUPPORT_STATIC_ALLOCATION=y
CONFIG_FREERTOS_DEBUG_OCDAWARE=y
# end of FreeRTOS

#
# Hardware Abstraction Layer (HAL) and Low Level (LL)
#
CONFIG_HAL_ASSERTION_EQUALS_SYSTEM=y
# CONFIG_HAL_ASSERTION_DISABLE is not set
CONFIG_HAL_DEFAULT_ASSERTION_LEVEL=0
CONFIG_HAL_SPI_MASTER_FUNC_IN_IRAM=y
CONFIG_HAL_SPI_SLAVE_FUNC_IN_IRAM=y
# end of Hardware Abstraction Layer (HAL) and Low Level (LL)

#
# Heap memory debugging
#
CONFIG_HEAP_POISONING_DISABLED=y
# CONFIG_HEAP_POISONING_LIGHT is not set
# CONFIG_HEAP_POISONING_COMPREHENSIVE is not set
CONFIG_HEAP_TRACING_OFF=y
# CONFIG_HEAP_TRACING_STANDALONE is not set
# CONFIG_HEAP_TRACING_TOHOST is not set
# default:
# CONFIG_HEAP_USE_HOOKS is not set
# default:
# CONFIG_HEAP_ABORT_WHEN_ALLOCATION_FAILS is not set
# default:
# CONFIG_HEAP_PLACE_FUNCTION_INTO_FLASH is not set
# end of Heap memory debugging

#
# Log output
#
# CONFIG_LOG_DEFAULT_LEVEL_NONE is not set
# CONFIG_LOG_DEFAULT_LEVEL_ERROR is not set
# CONFIG_LOG_DEFAULT_LEVEL_WARN is not set
CONFIG_LOG_DEFAULT_LEVEL_INFO=y
# CONFIG_LOG_DEFAULT_LEVEL_DEBUG is not set
# CONFIG_LOG_DEFAULT_LEVEL_VERBOSE is not set
CONFIG_LOG_DEFAULT_LEVEL=3
CONFIG_LOG_MAXIMUM_EQUALS_DEFAULT=y
# CONFIG_LOG_MAXIMUM_LEVEL_DEBUG is not set
# CONFIG_LOG_MAXIMUM_LEVEL_VERBOSE is not set
CONFIG_LOG_MAXIMUM_LEVEL=3
CONFIG_LOG_COLORS=y
CONFIG_LOG_TIMESTAMP_SOURCE_RTOS=y
# CONFIG_LOG_TIMESTAMP_SOURCE_SYSTEM is not set
# end of Log output

#
# LWIP
#
CONFIG_LWIP_LOCAL_HOSTNAME="espressif"
# default:
# CONFIG_LWIP_NETIF_API is not set
CONFIG_LWIP_TCPIP_TASK_PRIO=18
# default:
# CONFIG_LWIP_TCPIP_CORE_LOCKING is not set
# default:
# CONFIG_LWIP_CHECK_THREAD_SAFETY is not set
CONFIG_LWIP_DNS_SUPPORT_MDNS_QUERIES=y
# default:
# CONFIG_LWIP_L2_TO_L3_COPY is not set
# default:
# CONFIG_LWIP_IRAM_OPTIMIZATION is not set
# default:
# CONFIG_LWIP_EXTRA_IRAM_OPTIMIZATION is not set
CONFIG_LWIP_TIMERS_ONDEMAND=y
CONFIG_LWIP_ND6=y
# default:
# CONFIG_LWIP_FORCE_ROUTER_FORWARDING is not set
CONFIG_LWIP_MAX_SOCKETS=10
# default:
# CONFIG_LWIP_USE_ONLY_LWIP_SELECT is not set
# default:
# CONFIG_LWIP_SO_LINGER is not set
CONFIG_LWIP_SO_REUSE=y
CONFIG_LWIP_SO_REUSE_RXTOALL=y
# default:
# CONFIG_LWIP_SO_RCVBUF is not set
# default:
# CONFIG_LWIP_NETBUF_RECVINFO is not set
CONFIG_LWIP_IP_DEFAULT_TTL=64
CONFIG_LWIP_IP4_FRAG=y
CONFIG_LWIP_IP6_FRAG=y
# default:
# CONFIG_LWIP_IP4_REASSEMBLY is not set
# default:
# CONFIG_LWIP_IP6_REASSEMBLY is not set
CONFIG_LWIP_IP_REASS_MAX_PBUFS=10
# default:
# CONFIG_LWIP_IP_FORWARD is not set
# default:
# CONFIG_LWIP_STATS is not set
CONFIG_LWIP_ESP_GRATUITOUS_ARP=y
CONFIG_LWIP_GARP_TMR_INTERVAL=60
CONFIG_LWIP_ESP_MLDV6_REPORT=y
CONFIG_LWIP_MLDV6_TMR_INTERVAL=40
CONFIG_LWIP_TCPIP_RECVMBOX_SIZE=32
CONFIG_LWIP_DHCP_DOES_ARP_CHECK=y
# default:
# CONFIG_LWIP_DHCP_DISABLE_CLIENT_ID is not set
CONFIG_LWIP_DHCP_DISABLE_VENDOR_CLASS_ID=y
# default:
# CONFIG_LWIP_DHCP_RESTORE_LAST_IP is not set
CONFIG_LWIP_DHCP_OPTIONS_LEN=68
CONFIG_LWIP_NUM_NETIF_CLIENT_DATA=0
CONFIG_LWIP_DHCP_COARSE_TIMER_SECS=1

#
# DHCP server
#
CONFIG_LWIP_DHCPS=y
CONFIG_LWIP_DHCPS_LEASE_UNIT=60
CONFIG_LWIP_DHCPS_MAX_STATION_NUM=8
# end of DHCP server

# default:
# CONFIG_LWIP_AUTOIP is not set
CONFIG_LWIP_IPV4=y
CONFIG_LWIP_IPV6=y
# default:
# CONFIG_LWIP_IPV6_AUTOCONFIG is not set
CONFIG_LWIP_IPV6_NUM_ADDRESSES=3
# default:
# CONFIG_LWIP_IPV6_FORWARD is not set
# default:
# CONFIG_LWIP_NETIF_STATUS_CALLBACK is not set
CONFIG_LWIP_NETIF_LOOPBACK=y
CONFIG_LWIP_LOOPBACK_MAX_PBUFS=8

#
# TCP
#
CONFIG_LWIP_MAX_ACTIVE_TCP=16
CONFIG_LWIP_MAX_LISTENING_TCP=16
CONFIG_LWIP_TCP_HIGH_SPEED_RETRANSMISSION=y
CONFIG_LWIP_TCP_MAXRTX=12
CONFIG_LWIP_TCP_SYNMAXRTX=12
CONFIG_LWIP_TCP_MSS=1440
CONFIG_LWIP_TCP_TMR_INTERVAL=250
CONFIG_LWIP_TCP_MSL=60000
CONFIG_LWIP_TCP_FIN_WAIT_TIMEOUT=20000
CONFIG_LWIP_TCP_SND_BUF_DEFAULT=5744
CONFIG_LWIP_TCP_WND_DEFAULT=5744
CONFIG_LWIP_TCP_RECVMBOX_SIZE=6
CONFIG_LWIP_TCP_QUEUE_OOSEQ=y
CONFIG_LWIP_TCP_OOSEQ_TIMEOUT=6
CONFIG_LWIP_TCP_OOSEQ_MAX_PBUFS=4
# default:
# CONFIG_LWIP_TCP_SACK_OUT is not set
CONFIG_LWIP_TCP_OVERSIZE_MSS=y
# CONFIG_LWIP_TCP_OVERSIZE_QUARTER_MSS is not set
# CONFIG_LWIP_TCP_OVERSIZE_DISABLE is not set
CONFIG_LWIP_TCP_RTO_TIME=1500
# end of TCP

#
# UDP
#
CONFIG_LWIP_MAX_UDP_PCBS=16
CONFIG_LWIP_UDP_RECVMBOX_SIZE=6
# end of UDP

#
# Checksums
#
# default:
# CONFIG_LWIP_CHECKSUM_CHECK_IP is not set
# default:
# CONFIG_LWIP_CHECKSUM_CHECK_UDP is not set
CONFIG_LWIP_CHECKSUM_CHECK_ICMP=y
# end of Checksums

CONFIG_LWIP_TCPIP_TASK_STACK_SIZE=3072
CONFIG_LWIP_TCPIP_TASK_AFFINITY_NO_AFFINITY=y
# CONFIG_LWIP_TCPIP_TASK_AFFINITY_CPU0 is not set
# CONFIG_LWIP_TCPIP_TASK_AFFINITY_CPU1 is not set
CONFIG_LWIP_TCPIP_TASK_AFFINITY=0x7FFFFFFF
# default:
# CONFIG_LWIP_PPP_SUPPORT is not set
CONFIG_LWIP_IPV6_MEMP_NUM_ND6_QUEUE=3
CONFIG_LWIP_IPV6_ND6_NUM_NEIGHBORS=5
# default:
# CONFIG_LWIP_SLIP_SUPPORT is not set

#
# ICMP
#
CONFIG_LWIP_ICMP=y
# default:
# CONFIG_LWIP_MULTICAST_PING is not set
# default:
# CONFIG_LWIP_BROADCAST_PING is not set
# end of ICMP

#
# LWIP RAW API
#
CONFIG_LWIP_MAX_RAW_PCBS=16
# end of LWIP RAW API

#
# SNTP
#
CONFIG_LWIP_SNTP_MAX_SERVERS=1
# default:
# CONFIG_LWIP_DHCP_GET_NTP_SRV is not set
CONFIG_LWIP_SNTP_UPDATE_DELAY=3600000
# end of SNTP

#
# DNS
#
CONFIG_LWIP_DNS_MAX_SERVERS=3
# default:
# CONFIG_LWIP_FALLBACK_DNS_SERVER_SUPPORT is not set
# end of DNS

CONFIG_LWIP_BRIDGEIF_MAX_PORTS=7

#
# Hooks
#
# CONFIG_LWIP_HOOK_TCP_ISN_NONE is not set
CONFIG_LWIP_HOOK_TCP_ISN_DEFAULT=y
# CONFIG_LWIP_HOOK_TCP_ISN_CUSTOM is not set
CONFIG_LWIP_HOOK_IP6_ROUTE_NONE=y
# CONFIG_LWIP_HOOK_IP6_ROUTE_DEFAULT is not set
# CONFIG_LWIP_HOOK_IP6_ROUTE_CUSTOM is not set
CONFIG_LWIP_HOOK_ND6_GET_GW_NONE=y
# CONFIG_LWIP_HOOK_ND6_GET_GW_DEFAULT is not set
# CONFIG_LWIP_HOOK_ND6_GET_GW_CUSTOM is not set
CONFIG_LWIP_HOOK_IP6_SELECT_SRC_ADDR_NONE=y
# CONFIG_LWIP_HOOK_IP6_SELECT_SRC_ADDR_DEFAULT is not set
# CONFIG_LWIP_HOOK_IP6_SELECT_SRC_ADDR_CUSTOM is not set
CONFIG_LWIP_HOOK_NETCONN_EXT_RESOLVE_NONE=y
# CONFIG_LWIP_HOOK_NETCONN_EXT_RESOLVE_DEFAULT is not set
# CONFIG_LWIP_HOOK_NETCONN_EXT_RESOLVE_CUSTOM is not set
CONFIG_LWIP_HOOK_IP6_INPUT_NONE=y
# CONFIG_LWIP_HOOK_IP6_INPUT_DEFAULT is not set
# CONFIG_LWIP_HOOK_IP6_INPUT_CUSTOM is not set
# end of Hooks

# default:
# CONFIG_LWIP_DEBUG is not set
# end of LWIP

#
# mbedTLS
#
CONFIG_MBEDTLS_INTERNAL_MEM_ALLOC=y
# CONFIG_MBEDTLS_EXTERNAL_MEM_ALLOC is not set
# CONFIG_MBEDTLS_DEFAULT_MEM_ALLOC is not set
# CONFIG_MBEDTLS_CUSTOM_MEM_ALLOC is not set
CONFIG_MBEDTLS_ASYMMETRIC_CONTENT_LEN=y
CONFIG_MBEDTLS_SSL_IN_CONTENT_LEN=16384
CONFIG_MBEDTLS_SSL_OUT_CONTENT_LEN=4096
# default:
# CONFIG_MBEDTLS_DYNAMIC_BUFFER is not set
# default:
# CONFIG_MBEDTLS_DEBUG is not set

#
# mbedTLS v3.x related
#
# default:
# CONFIG_MBEDTLS_SSL_PROTO_TLS1_3 is not set
# default:
# CONFIG_MBEDTLS_SSL_VARIABLE_BUFFER_LENGTH is not set
# default:
# CONFIG_MBEDTLS_X509_TRUSTED_CERT_CALLBACK is not set
# default:
# CONFIG_MBEDTLS_SSL_CONTEXT_SERIALIZATION is not set
CONFIG_MBEDTLS_SSL_KEEP_PEER_CERTIFICATE=y
CONFIG_MBEDTLS_PKCS7_C=y
# end of mbedTLS v3.x related

#
# Certificate Bundle
#
CONFIG_MBEDTLS_CERTIFICATE_BUNDLE=y
CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_FULL=y
# CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_CMN is not set
# CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_NONE is not set
# default:
# CONFIG_MBEDTLS_CUSTOM_CERTIFICATE_BUNDLE is not set
CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_MAX_CERTS=200
# end of Certificate Bundle

# default:
# CONFIG_MBEDTLS_ECP_RESTARTABLE is not set
CONFIG_MBEDTLS_CMAC_C=y
CONFIG_MBEDTLS_HARDWARE_AES=y
CONFIG_MBEDTLS_GCM_SUPPORT_NON_AES_CIPHER=y
CONFIG_MBEDTLS_HARDWARE_MPI=y
CONFIG_MBEDTLS_HARDWARE_SHA=y
CONFIG_MBEDTLS_ROM_MD5=y
# default:
# CONFIG_MBEDTLS_ATCA_HW_ECDSA_SIGN is not set
# default:
# CONFIG_MBEDTLS_ATCA_HW_ECDSA_VERIFY is not set
CONFIG_MBEDTLS_HAVE_TIME=y
# default:
# CONFIG_MBEDTLS_PLATFORM_TIME_ALT is not set
# default:
# CONFIG_MBEDTLS_HAVE_TIME_DATE is not set
CONFIG_MBEDTLS_ECDSA_DETERMINISTIC=y
CONFIG_MBEDTLS_SHA512_C=y
CONFIG_MBEDTLS_TLS_SERVER_AND_CLIENT=y
# CONFIG_MBEDTLS_TLS_SERVER_ONLY is not set
# CONFIG_MBEDTLS_TLS_CLIENT_ONLY is not set
# CONFIG_MBEDTLS_TLS_DISABLED is not set
CONFIG_MBEDTLS_TLS_SERVER=y
CONFIG_MBEDTLS_TLS_CLIENT=y
CONFIG_MBEDTLS_TLS_ENABLED=y

#
# TLS Key Exchange Methods
#
# default:
# CONFIG_MBEDTLS_PSK_MODES is not set
CONFIG_MBEDTLS_KEY_EXCHANGE_RSA=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ELLIPTIC_CURVE=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ECDHE_RSA=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ECDH_RSA=y
# end of TLS Key Exchange Methods

CONFIG_MBEDTLS_SSL_RENEGOTIATION=y
CONFIG_MBEDTLS_SSL_PROTO_TLS1_2=y
# default:
# CONFIG_MBEDTLS_SSL_PROTO_GMTSSL1_1 is not set
# default:
# CONFIG_MBEDTLS_SSL_PROTO_DTLS is not set
CONFIG_MBEDTLS_SSL_ALPN=y
CONFIG_MBEDTLS_CLIENT_SSL_SESSION_TICKETS=y
CONFIG_MBEDTLS_SERVER_SSL_SESSION_TICKETS=y

#
# Symmetric Ciphers
#
CONFIG_MBEDTLS_AES_C=y
# default:
# CONFIG_MBEDTLS_CAMELLIA_C is not set
# default:
# CONFIG_MBEDTLS_DES_C is not set
# default:
# CONFIG_MBEDTLS_BLOWFISH_C is not set
# default:
# CONFIG_MBEDTLS_XTEA_C is not set
CONFIG_MBEDTLS_CCM_C=y
CONFIG_MBEDTLS_GCM_C=y
# default:
# CONFIG_MBEDTLS_NIST_KW_C is not set
# end of Symmetric Ciphers

# default:
# CONFIG_MBEDTLS_RIPEMD160_C is not set

#
# Certificates
#
CONFIG_MBEDTLS_PEM_PARSE_C=y
CONFIG_MBEDTLS_PEM_WRITE_C=y
CONFIG_MBEDTLS_X509_CRL_PARSE_C=y
CONFIG_MBEDTLS_X509_CSR_PARSE_C=y
# end of Certificates

CONFIG_MBEDTLS_ECP_C=y
# default:
# CONFIG_MBEDTLS_DHM_C is not set
CONFIG_MBEDTLS_ECDH_C=y
CONFIG_MBEDTLS_ECDSA_C=y
# default:
# CONFIG_MBEDTLS_ECJPAKE_C is not set
CONFIG_MBEDTLS_ECP_DP_SECP192R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP224R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP256R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP384R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP521R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP192K1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP224K1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP256K1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_BP256R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_BP384R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_BP512R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_CURVE25519_ENABLED=y
CONFIG_MBEDTLS_ECP_NIST_OPTIM=y
CONFIG_MBEDTLS_ECP_FIXED_POINT_OPTIM=y
# default:
# CONFIG_MBEDTLS_POLY1305_C is not set
# default:
# CONFIG_MBEDTLS_CHACHA20_C is not set
# default:
# CONFIG_MBEDTLS_HKDF_C is not set
# default:
# CONFIG_MBEDTLS_THREADING_C is not set
# default:
# CONFIG_MBEDTLS_LARGE_KEY_SOFTWARE_MPI is not set
# end of mbedTLS

#
# ESP-MQTT Configurations
#
CONFIG_MQTT_PROTOCOL_311=y
# default:
# CONFIG_MQTT_PROTOCOL_5 is not set
CONFIG_MQTT_TRANSPORT_SSL=y
CONFIG_MQTT_TRANSPORT_WEBSOCKET=y
CONFIG_MQTT_TRANSPORT_WEBSOCKET_SECURE=y
# default:
# CONFIG_MQTT_MSG_ID_INCREMENTAL is not set
# default:
# CONFIG_MQTT_SKIP_PUBLISH_IF_DISCONNECTED is not set
# default:
# CONFIG_MQTT_REPORT_DELETED_MESSAGES is not set
# default:
# CONFIG_MQTT_USE_CUSTOM_CONFIG is not set
# default:
# CONFIG_MQTT_TASK_CORE_SELECTION_ENABLED is not set
# default:
# CONFIG_MQTT_CUSTOM_OUTBOX is not set
# end of ESP-MQTT Configurations

#
# Newlib
#
CONFIG_NEWLIB_STDOUT_LINE_ENDING_CRLF=y
# CONFIG_NEWLIB_STDOUT_LINE_ENDING_LF is not set
# CONFIG_NEWLIB_STDOUT_LINE_ENDING_CR is not set
# CONFIG_NEWLIB_STDIN_LINE_ENDING_CRLF is not set
# CONFIG_NEWLIB_STDIN_LINE_ENDING_LF is not set
CONFIG_NEWLIB_STDIN_LINE_ENDING_CR=y
# default:
# CONFIG_NEWLIB_NANO_FORMAT is not set
CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC_HRT=y
# CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC is not set
# CONFIG_NEWLIB_TIME_SYSCALL_USE_HRT is not set
# CONFIG_NEWLIB_TIME_SYSCALL_USE_NONE is not set
# end of Newlib

#
# NVS
#
# default:
# CONFIG_NVS_ASSERT_ERROR_CHECK is not set
# end of NVS

#
# OpenThread
#
# default:
# CONFIG_OPENTHREAD_ENABLED is not set

#
# Thread Operational Dataset
#
CONFIG_OPENTHREAD_NETWORK_NAME="OpenThread-ESP"
CONFIG_OPENTHREAD_MESH_LOCAL_PREFIX="fd00:db8:a0:0::/64"
CONFIG_OPENTHREAD_NETWORK_CHANNEL=15
CONFIG_OPENTHREAD_NETWORK_PANID=0x1234
CONFIG_OPENTHREAD_NETWORK_EXTPANID="dead00beef00cafe"
CONFIG_OPENTHREAD_NETWORK_MASTERKEY="00112233445566778899aabbccddeeff"
CONFIG_OPENTHREAD_NETWORK_PSKC="104810e2315100afd6bc9215a6bfac53"
# end of Thread Operational Dataset

CONFIG_OPENTHREAD_XTAL_ACCURACY=130
# default:
# CONFIG_OPENTHREAD_SPINEL_ONLY is not set
# default:
# CONFIG_OPENTHREAD_RX_ON_WHEN_IDLE is not set

#
# Thread Address Query Config
#
# end of Thread Address Query Config
# end of OpenThread

#
# Protocomm
#
CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_0=y
CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_1=y
# default:
# CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_2 is not set
# end of Protocomm

#
# PThreads
#
CONFIG_PTHREAD_TASK_PRIO_DEFAULT=5
CONFIG_PTHREAD_TASK_STACK_SIZE_DEFAULT=3072
CONFIG_PTHREAD_STACK_MIN=768
CONFIG_PTHREAD_DEFAULT_CORE_NO_AFFINITY=y
# CONFIG_PTHREAD_DEFAULT_CORE_0 is not set
# CONFIG_PTHREAD_DEFAULT_CORE_1 is not set
CONFIG_PTHREAD_TASK_CORE_DEFAULT=-1
CONFIG_PTHREAD_TASK_NAME_DEFAULT="pthread"
# end of PThreads

#
# MMU Config
#
CONFIG_MMU_PAGE_SIZE_64KB=y
CONFIG_MMU_PAGE_MODE="64KB"
CONFIG_MMU_PAGE_SIZE=0x10000
# end of MMU Config

#
# Main Flash configuration
#

#
# SPI Flash behavior when brownout
#
CONFIG_SPI_FLASH_BROWNOUT_RESET_XMC=y
CONFIG_SPI_FLASH_BROWNOUT_RESET=y
# end of SPI Flash behavior when brownout

#
# Optional and Experimental Features (READ DOCS FIRST)
#

#
# Features here require specific hardware (READ DOCS FIRST!)
#
# end of Optional and Experimental Features (READ DOCS FIRST)
# end of Main Flash configuration

#
# SPI Flash driver
#
# default:
# CONFIG_SPI_FLASH_VERIFY_WRITE is not set
# default:
# CONFIG_SPI_FLASH_ENABLE_COUNTERS is not set
CONFIG_SPI_FLASH_ROM_DRIVER_PATCH=y
CONFIG_SPI_FLASH_DANGEROUS_WRITE_ABORTS=y
# CONFIG_SPI_FLASH_DANGEROUS_WRITE_FAILS is not set
# CONFIG_SPI_FLASH_DANGEROUS_WRITE_ALLOWED is not set
# default:
# CONFIG_SPI_FLASH_SHARE_SPI1_BUS is not set
# default:
# CONFIG_SPI_FLASH_BYPASS_BLOCK_ERASE is not set
CONFIG_SPI_FLASH_YIELD_DURING_ERASE=y
CONFIG_SPI_FLASH_ERASE_YIELD_DURATION_MS=20
CONFIG_SPI_FLASH_ERASE_YIELD_TICKS=1
CONFIG_SPI_FLASH_WRITE_CHUNK_SIZE=8192
# default:
# CONFIG_SPI_FLASH_SIZE_OVERRIDE is not set
# default:
# CONFIG_SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED is not set
# default:
# CONFIG_SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST is not set

#
# Auto-detect flash chips
#
CONFIG_SPI_FLASH_VENDOR_XMC_SUPPORTED=y
CONFIG_SPI_FLASH_VENDOR_GD_SUPPORTED=y
CONFIG_SPI_FLASH_VENDOR_ISSI_SUPPORTED=y
CONFIG_SPI_FLASH_VENDOR_MXIC_SUPPORTED=y
CONFIG_SPI_FLASH_VENDOR_WINBOND_SUPPORTED=y
CONFIG_SPI_FLASH_SUPPORT_ISSI_CHIP=y
CONFIG_SPI_FLASH_SUPPORT_MXIC_CHIP=y
CONFIG_SPI_FLASH_SUPPORT_GD_CHIP=y
CONFIG_SPI_FLASH_SUPPORT_WINBOND_CHIP=y
# default:
# CONFIG_SPI_FLASH_SUPPORT_BOYA_CHIP is not set
# default:
# CONFIG_SPI_FLASH_SUPPORT_TH_CHIP is not set
# end of Auto-detect flash chips

CONFIG_SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE=y
# end of SPI Flash driver

#
# SPIFFS Configuration
#
CONFIG_SPIFFS_MAX_PARTITIONS=3

#
# SPIFFS Cache Configuration
#
CONFIG_SPIFFS_CACHE=y
CONFIG_SPIFFS_CACHE_WR=y
# default:
# CONFIG_SPIFFS_CACHE_STATS is not set
# end of SPIFFS Cache Configuration

CONFIG_SPIFFS_PAGE_CHECK=y
CONFIG_SPIFFS_GC_MAX_RUNS=10
# default:
# CONFIG_SPIFFS_GC_STATS is not set
CONFIG_SPIFFS_PAGE_SIZE=256
CONFIG_SPIFFS_OBJ_NAME_LEN=32
# default:
# CONFIG_SPIFFS_FOLLOW_SYMLINKS is not set
CONFIG_SPIFFS_USE_MAGIC=y
CONFIG_SPIFFS_USE_MAGIC_LENGTH=y
CONFIG_SPIFFS_META_LENGTH=4
CONFIG_SPIFFS_USE_MTIME=y

#
# Debug Configuration
#
# default:
# CONFIG_SPIFFS_DBG is not set
# default:
# CONFIG_SPIFFS_API_DBG is not set
# default:
# CONFIG_SPIFFS_GC_DBG is not set
# default:
# CONFIG_SPIFFS_CACHE_DBG is not set
# default:
# CONFIG_SPIFFS_CHECK_DBG is not set
# default:
# CONFIG_SPIFFS_TEST_VISUALISATION is not set
# end of Debug Configuration
# end of SPIFFS Configuration

#
# TCP Transport
#

#
# Websocket
#
CONFIG_WS_TRANSPORT=y
CONFIG_WS_BUFFER_SIZE=1024
# default:
# CONFIG_WS_DYNAMIC_BUFFER is not set
# end of Websocket
# end of TCP Transport

#
# Ultra Low Power (ULP) Co-processor
#
# default:
# CONFIG_ULP_COPROC_ENABLED is not set
# end of Ultra Low Power (ULP) Co-processor

#
# Unity unit testing library
#
CONFIG_UNITY_ENABLE_FLOAT=y
CONFIG_UNITY_ENABLE_DOUBLE=y
# default:
# CONFIG_UNITY_ENABLE_64BIT is not set
# default:
# CONFIG_UNITY_ENABLE_COLOR is not set
CONFIG_UNITY_ENABLE_IDF_TEST_RUNNER=y
# default:
# CONFIG_UNITY_ENABLE_FIXTURE is not set
# default:
# CONFIG_UNITY_ENABLE_BACKTRACE_ON_FAIL is not set
# end of Unity unit testing library

#
# Virtual file system
#
CONFIG_VFS_SUPPORT_IO=y
CONFIG_VFS_SUPPORT_DIR=y
CONFIG_VFS_SUPPORT_SELECT=y
CONFIG_VFS_SUPPRESS_SELECT_DEBUG_OUTPUT=y
CONFIG_VFS_SELECT_IN_RAM=y
CONFIG_VFS_SUPPORT_TERMIOS=y
CONFIG_VFS_MAX_COUNT=8

#
# Host File System I/O (Semihosting)
#
CONFIG_VFS_SEMIHOSTFS_MAX_MOUNT_POINTS=1
# end of Host File System I/O (Semihosting)
# end of Virtual file system

#
# Wear Levelling
#
# CONFIG_WL_SECTOR_SIZE_512 is not set
CONFIG_WL_SECTOR_SIZE_4096=y
CONFIG_WL_SECTOR_SIZE=4096
# end of Wear Levelling

#
# Wi-Fi Provisioning Manager
#
CONFIG_WIFI_PROV_SCAN_MAX_ENTRIES=16
CONFIG_WIFI_PROV_AUTOSTOP_TIMEOUT=30
# default:
# CONFIG_WIFI_PROV_BLE_BONDING is not set
CONFIG_WIFI_PROV_BLE_FORCE_ENCRYPTION=y
# default:
# CONFIG_WIFI_PROV_KEEP_BLE_ON_AFTER_PROV is not set
CONFIG_WIFI_PROV_STA_ALL_CHANNEL_SCAN=y
# CONFIG_WIFI_PROV_STA_FAST_SCAN is not set
# end of Wi-Fi Provisioning Manager
# end of Component config

# default:
# CONFIG_IDF_EXPERIMENTAL_FEATURES is not set

# Deprecated options for backward compatibility
# CONFIG_APP_BUILD_TYPE_ELF_RAM is not set
# default:
# CONFIG_NO_BLOBS is not set
# default:
# CONFIG_ESP32_NO_BLOBS is not set
# default:
# CONFIG_ESP32_COMPATIBLE_PRE_V2_1_BOOTLOADERS is not set
# default:
# CONFIG_ESP32_COMPATIBLE_PRE_V3_1_BOOTLOADERS is not set
# CONFIG_LOG_BOOTLOADER_LEVEL_NONE is not set
# CONFIG_LOG_BOOTLOADER_LEVEL_ERROR is not set
# CONFIG_LOG_BOOTLOADER_LEVEL_WARN is not set
CONFIG_LOG_BOOTLOADER_LEVEL_INFO=y
# CONFIG_LOG_BOOTLOADER_LEVEL_DEBUG is not set
# CONFIG_LOG_BOOTLOADER_LEVEL_VERBOSE is not set
CONFIG_LOG_BOOTLOADER_LEVEL=3
# default:
# CONFIG_APP_ROLLBACK_ENABLE is not set
# default:
# CONFIG_FLASH_ENCRYPTION_ENABLED is not set
# CONFIG_FLASHMODE_QIO is not set
# CONFIG_FLASHMODE_QOUT is not set
CONFIG_FLASHMODE_DIO=y
# CONFIG_FLASHMODE_DOUT is not set
CONFIG_MONITOR_BAUD=115200
# CONFIG_OPTIMIZATION_LEVEL_DEBUG is not set
# CONFIG_COMPILER_OPTIMIZATION_LEVEL_DEBUG is not set
# CONFIG_OPTIMIZATION_LEVEL_RELEASE is not set
# CONFIG_COMPILER_OPTIMIZATION_LEVEL_RELEASE is not set
# CONFIG_OPTIMIZATION_ASSERTIONS_ENABLED is not set
# CONFIG_OPTIMIZATION_ASSERTIONS_SILENT is not set
CONFIG_OPTIMIZATION_ASSERTIONS_DISABLED=y
CONFIG_OPTIMIZATION_ASSERTION_LEVEL=0
# default:
# CONFIG_CXX_EXCEPTIONS is not set
CONFIG_STACK_CHECK_NONE=y
# CONFIG_STACK_CHECK_NORM is not set
# CONFIG_STACK_CHECK_STRONG is not set
# CONFIG_STACK_CHECK_ALL is not set
# default:
# CONFIG_WARN_WRITE_STRINGS is not set
# CONFIG_ESP32_APPTRACE_DEST_TRAX is not set
CONFIG_ESP32_APPTRACE_DEST_NONE=y
CONFIG_ESP32_APPTRACE_LOCK_ENABLE=y
CONFIG_BLUEDROID_ENABLED=y
# CONFIG_NIMBLE_ENABLED is not set
CONFIG_BTC_TASK_STACK_SIZE=16384
CONFIG_BLUEDROID_PINNED_TO_CORE_0=y
# CONFIG_BLUEDROID_PINNED_TO_CORE_1 is not set
CONFIG_BLUEDROID_PINNED_TO_CORE=0
CONFIG_BTU_TASK_STACK_SIZE=8192
# default:
# CONFIG_BLUEDROID_MEM_DEBUG is not set
CONFIG_CLASSIC_BT_ENABLED=y
CONFIG_A2DP_ENABLE=y
# default:
# CONFIG_HFP_ENABLE is not set
CONFIG_GATTS_ENABLE=y
# CONFIG_GATTS_SEND_SERVICE_CHANGE_MANUAL is not set
CONFIG_GATTS_SEND_SERVICE_CHANGE_AUTO=y
CONFIG_GATTS_SEND_SERVICE_CHANGE_MODE=0
CONFIG_GATTC_ENABLE=y
# default:
# CONFIG_GATTC_CACHE_NVS_FLASH is not set
CONFIG_BLE_SMP_ENABLE=y
# default:
# CONFIG_SMP_SLAVE_CON_PARAMS_UPD_ENABLE is not set
# CONFIG_HCI_TRACE_LEVEL_NONE is not set
# CONFIG_HCI_TRACE_LEVEL_ERROR is not set
CONFIG_HCI_TRACE_LEVEL_WARNING=y
# CONFIG_HCI_TRACE_LEVEL_API is not set
# CONFIG_HCI_TRACE_LEVEL_EVENT is not set
# CONFIG_HCI_TRACE_LEVEL_DEBUG is not set
# CONFIG_HCI_TRACE_LEVEL_VERBOSE is not set
CONFIG_HCI_INITIAL_TRACE_LEVEL=2
# CONFIG_BTM_TRACE_LEVEL_NONE is not set
# CONFIG_BTM_TRACE_LEVEL_ERROR is not set
CONFIG_BTM_TRACE_LEVEL_WARNING=y
# CONFIG_BTM_TRACE_LEVEL_API is not set
# CONFIG_BTM_TRACE_LEVEL_EVENT is not set
# CONFIG_BTM_TRACE_LEVEL_DEBUG is not set
# CONFIG_BTM_TRACE_LEVEL_VERBOSE is not set
CONFIG_BTM_INITIAL_TRACE_LEVEL=2
# CONFIG_L2CAP_TRACE_LEVEL_NONE is not set
# CONFIG_L2CAP_TRACE_LEVEL_ERROR is not set
CONFIG_L2CAP_TRACE_LEVEL_WARNING=y
# CONFIG_L2CAP_TRACE_LEVEL_API is not set
# CONFIG_L2CAP_TRACE_LEVEL_EVENT is not set
# CONFIG_L2CAP_TRACE_LEVEL_DEBUG is not set
# CONFIG_L2CAP_TRACE_LEVEL_VERBOSE is not set
CONFIG_L2CAP_INITIAL_TRACE_LEVEL=2
# CONFIG_RFCOMM_TRACE_LEVEL_NONE is not set
# CONFIG_RFCOMM_TRACE_LEVEL_ERROR is not set
CONFIG_RFCOMM_TRACE_LEVEL_WARNING=y
# CONFIG_RFCOMM_TRACE_LEVEL_API is not set
# CONFIG_RFCOMM_TRACE_LEVEL_EVENT is not set
# CONFIG_RFCOMM_TRACE_LEVEL_DEBUG is not set
# CONFIG_RFCOMM_TRACE_LEVEL_VERBOSE is not set
CONFIG_RFCOMM_INITIAL_TRACE_LEVEL=2
# CONFIG_SDP_TRACE_LEVEL_NONE is not set
# CONFIG_SDP_TRACE_LEVEL_ERROR is not set
CONFIG_SDP_TRACE_LEVEL_WARNING=y
# CONFIG_SDP_TRACE_LEVEL_API is not set
# CONFIG_SDP_TRACE_LEVEL_EVENT is not set
# CONFIG_SDP_TRACE_LEVEL_DEBUG is not set
# CONFIG_SDP_TRACE_LEVEL_VERBOSE is not set
CONFIG_BTH_LOG_SDP_INITIAL_TRACE_LEVEL=2
# CONFIG_GAP_TRACE_LEVEL_NONE is not set
# CONFIG_GAP_TRACE_LEVEL_ERROR is not set
CONFIG_GAP_TRACE_LEVEL_WARNING=y
# CONFIG_GAP_TRACE_LEVEL_API is not set
# CONFIG_GAP_TRACE_LEVEL_EVENT is not set
# CONFIG_GAP_TRACE_LEVEL_DEBUG is not set
# CONFIG_GAP_TRACE_LEVEL_VERBOSE is not set
CONFIG_GAP_INITIAL_TRACE_LEVEL=2
CONFIG_BNEP_INITIAL_TRACE_LEVEL=2
# CONFIG_PAN_TRACE_LEVEL_NONE is not set
# CONFIG_PAN_TRACE_LEVEL_ERROR is not set
CONFIG_PAN_TRACE_LEVEL_WARNING=y
# CONFIG_PAN_TRACE_LEVEL_API is not set
# CONFIG_PAN_TRACE_LEVEL_EVENT is not set
# CONFIG_PAN_TRACE_LEVEL_DEBUG is not set
# CONFIG_PAN_TRACE_LEVEL_VERBOSE is not set
CONFIG_PAN_INITIAL_TRACE_LEVEL=2
# CONFIG_A2D_TRACE_LEVEL_NONE is not set
# CONFIG_A2D_TRACE_LEVEL_ERROR is not set
CONFIG_A2D_TRACE_LEVEL_WARNING=y
# CONFIG_A2D_TRACE_LEVEL_API is not set
# CONFIG_A2D_TRACE_LEVEL_EVENT is not set
# CONFIG_A2D_TRACE_LEVEL_DEBUG is not set
# CONFIG_A2D_TRACE_LEVEL_VERBOSE is not set
CONFIG_A2D_INITIAL_TRACE_LEVEL=2
# CONFIG_AVDT_TRACE_LEVEL_NONE is not set
# CONFIG_AVDT_TRACE_LEVEL_ERROR is not set
CONFIG_AVDT_TRACE_LEVEL_WARNING=y
# CONFIG_AVDT_TRACE_LEVEL_API is not set
# CONFIG_AVDT_TRACE_LEVEL_EVENT is not set
# CONFIG_AVDT_TRACE_LEVEL_DEBUG is not set
# CONFIG_AVDT_TRACE_LEVEL_VERBOSE is not set
CONFIG_AVDT_INITIAL_TRACE_LEVEL=2
# CONFIG_AVCT_TRACE_LEVEL_NONE is not set
# CONFIG_AVCT_TRACE_LEVEL_ERROR is not set
CONFIG_AVCT_TRACE_LEVEL_WARNING=y
# CONFIG_AVCT_TRACE_LEVEL_API is not set
# CONFIG_AVCT_TRACE_LEVEL_EVENT is not set
# CONFIG_AVCT_TRACE_LEVEL_DEBUG is not set
# CONFIG_AVCT_TRACE_LEVEL_VERBOSE is not set
CONFIG_AVCT_INITIAL_TRACE_LEVEL=2
# CONFIG_AVRC_TRACE_LEVEL_NONE is not set
# CONFIG_AVRC_TRACE_LEVEL_ERROR is not set
CONFIG_AVRC_TRACE_LEVEL_WARNING=y
# CONFIG_AVRC_TRACE_LEVEL_API is not set
# CONFIG_AVRC_TRACE_LEVEL_EVENT is not set
# CONFIG_AVRC_TRACE_LEVEL_DEBUG is not set
# CONFIG_AVRC_TRACE_LEVEL_VERBOSE is not set
CONFIG_AVRC_INITIAL_TRACE_LEVEL=2
# CONFIG_MCA_TRACE_LEVEL_NONE is not set
# CONFIG_MCA_TRACE_LEVEL_ERROR is not set
CONFIG_MCA_TRACE_LEVEL_WARNING=y
# CONFIG_MCA_TRACE_LEVEL_API is not set
# CONFIG_MCA_TRACE_LEVEL_EVENT is not set
# CONFIG_MCA_TRACE_LEVEL_DEBUG is not set
# CONFIG_MCA_TRACE_LEVEL_VERBOSE is not set
CONFIG_MCA_INITIAL_TRACE_LEVEL=2
# CONFIG_HID_TRACE_LEVEL_NONE is not set
# CONFIG_HID_TRACE_LEVEL_ERROR is not set
CONFIG_HID_TRACE_LEVEL_WARNING=y
# CONFIG_HID_TRACE_LEVEL_API is not set
# CONFIG_HID_TRACE_LEVEL_EVENT is not set
# CONFIG_HID_TRACE_LEVEL_DEBUG is not set
# CONFIG_HID_TRACE_LEVEL_VERBOSE is not set
CONFIG_HID_INITIAL_TRACE_LEVEL=2
# CONFIG_APPL_TRACE_LEVEL_NONE is not set
# CONFIG_APPL_TRACE_LEVEL_ERROR is not set
CONFIG_APPL_TRACE_LEVEL_WARNING=y
# CONFIG_APPL_TRACE_LEVEL_API is not set
# CONFIG_APPL_TRACE_LEVEL_EVENT is not set
# CONFIG_APPL_TRACE_LEVEL_DEBUG is not set
# CONFIG_APPL_TRACE_LEVEL_VERBOSE is not set
CONFIG_APPL_INITIAL_TRACE_LEVEL=2
# CONFIG_GATT_TRACE_LEVEL_NONE is not set
# CONFIG_GATT_TRACE_LEVEL_ERROR is not set
CONFIG_GATT_TRACE_LEVEL_WARNING=y
# CONFIG_GATT_TRACE_LEVEL_API is not set
# CONFIG_GATT_TRACE_LEVEL_EVENT is not set
# CONFIG_GATT_TRACE_LEVEL_DEBUG is not set
# CONFIG_GATT_TRACE_LEVEL_VERBOSE is not set
CONFIG_GATT_INITIAL_TRACE_LEVEL=2
# CONFIG_SMP_TRACE_LEVEL_NONE is not set
# CONFIG_SMP_TRACE_LEVEL_ERROR is not set
CONFIG_SMP_TRACE_LEVEL_WARNING=y
# CONFIG_SMP_TRACE_LEVEL_API is not set
# CONFIG_SMP_TRACE_LEVEL_EVENT is not set
# CONFIG_SMP_TRACE_LEVEL_DEBUG is not set
# CONFIG_SMP_TRACE_LEVEL_VERBOSE is not set
CONFIG_SMP_INITIAL_TRACE_LEVEL=2
# CONFIG_BTIF_TRACE_LEVEL_NONE is not set
# CONFIG_BTIF_TRACE_LEVEL_ERROR is not set
CONFIG_BTIF_TRACE_LEVEL_WARNING=y
# CONFIG_BTIF_TRACE_LEVEL_API is not set
# CONFIG_BTIF_TRACE_LEVEL_EVENT is not set
# CONFIG_BTIF_TRACE_LEVEL_DEBUG is not set
# CONFIG_BTIF_TRACE_LEVEL_VERBOSE is not set
CONFIG_BTIF_INITIAL_TRACE_LEVEL=2
# CONFIG_BTC_TRACE_LEVEL_NONE is not set
# CONFIG_BTC_TRACE_LEVEL_ERROR is not set
CONFIG_BTC_TRACE_LEVEL_WARNING=y
# CONFIG_BTC_TRACE_LEVEL_API is not set
# CONFIG_BTC_TRACE_LEVEL_EVENT is not set
# CONFIG_BTC_TRACE_LEVEL_DEBUG is not set
# CONFIG_BTC_TRACE_LEVEL_VERBOSE is not set
CONFIG_BTC_INITIAL_TRACE_LEVEL=2
# CONFIG_OSI_TRACE_LEVEL_NONE is not set
# CONFIG_OSI_TRACE_LEVEL_ERROR is not set
CONFIG_OSI_TRACE_LEVEL_WARNING=y
# CONFIG_OSI_TRACE_LEVEL_API is not set
# CONFIG_OSI_TRACE_LEVEL_EVENT is not set
# CONFIG_OSI_TRACE_LEVEL_DEBUG is not set
# CONFIG_OSI_TRACE_LEVEL_VERBOSE is not set
CONFIG_OSI_INITIAL_TRACE_LEVEL=2
# CONFIG_BLUFI_TRACE_LEVEL_NONE is not set
# CONFIG_BLUFI_TRACE_LEVEL_ERROR is not set
CONFIG_BLUFI_TRACE_LEVEL_WARNING=y
# CONFIG_BLUFI_TRACE_LEVEL_API is not set
# CONFIG_BLUFI_TRACE_LEVEL_EVENT is not set
# CONFIG_BLUFI_TRACE_LEVEL_DEBUG is not set
# CONFIG_BLUFI_TRACE_LEVEL_VERBOSE is not set
CONFIG_BLUFI_INITIAL_TRACE_LEVEL=2
# default:
# CONFIG_BLE_HOST_QUEUE_CONGESTION_CHECK is not set
CONFIG_SMP_ENABLE=y
# default:
# CONFIG_BLE_ACTIVE_SCAN_REPORT_ADV_SCAN_RSP_INDIVIDUALLY is not set
CONFIG_BLE_ESTABLISH_LINK_CONNECTION_TIMEOUT=30
# CONFIG_BTDM_CONTROLLER_MODE_BLE_ONLY is not set
# CONFIG_BTDM_CONTROLLER_MODE_BR_EDR_ONLY is not set
CONFIG_BTDM_CONTROLLER_MODE_BTDM=y
CONFIG_BTDM_CONTROLLER_BLE_MAX_CONN=3
CONFIG_BTDM_CONTROLLER_BR_EDR_MAX_ACL_CONN=2
CONFIG_BTDM_CONTROLLER_BR_EDR_MAX_SYNC_CONN=0
CONFIG_BTDM_CONTROLLER_BLE_MAX_CONN_EFF=3
CONFIG_BTDM_CONTROLLER_BR_EDR_MAX_ACL_CONN_EFF=2
CONFIG_BTDM_CONTROLLER_BR_EDR_MAX_SYNC_CONN_EFF=0
CONFIG_BTDM_CONTROLLER_PINNED_TO_CORE=0
CONFIG_BTDM_CONTROLLER_HCI_MODE_VHCI=y
# CONFIG_BTDM_CONTROLLER_HCI_MODE_UART_H4 is not set
CONFIG_BTDM_CONTROLLER_MODEM_SLEEP=y
CONFIG_BLE_SCAN_DUPLICATE=y
CONFIG_SCAN_DUPLICATE_BY_DEVICE_ADDR=y
# CONFIG_SCAN_DUPLICATE_BY_ADV_DATA is not set
# CONFIG_SCAN_DUPLICATE_BY_ADV_DATA_AND_DEVICE_ADDR is not set
CONFIG_SCAN_DUPLICATE_TYPE=0
CONFIG_DUPLICATE_SCAN_CACHE_SIZE=100
# default:
# CONFIG_BLE_MESH_SCAN_DUPLICATE_EN is not set
CONFIG_BTDM_CONTROLLER_FULL_SCAN_SUPPORTED=y
CONFIG_BLE_ADV_REPORT_FLOW_CONTROL_SUPPORTED=y
CONFIG_BLE_ADV_REPORT_FLOW_CONTROL_NUM=100
CONFIG_BLE_ADV_REPORT_DISCARD_THRSHOLD=20
CONFIG_ADC2_DISABLE_DAC=y
# default:
# CONFIG_MCPWM_ISR_IN_IRAM is not set
CONFIG_SW_COEXIST_ENABLE=y
CONFIG_ESP32_WIFI_SW_COEXIST_ENABLE=y
CONFIG_ESP_WIFI_SW_COEXIST_ENABLE=y
# default:
# CONFIG_EVENT_LOOP_PROFILING is not set
CONFIG_POST_EVENTS_FROM_ISR=y
CONFIG_POST_EVENTS_FROM_IRAM_ISR=y
# default:
# CONFIG_OTA_ALLOW_HTTP is not set
# CONFIG_TWO_UNIVERSAL_MAC_ADDRESS is not set
CONFIG_FOUR_UNIVERSAL_MAC_ADDRESS=y
CONFIG_NUMBER_OF_UNIVERSAL_MAC_ADDRESS=4
CONFIG_ESP32_DEEP_SLEEP_WAKEUP_DELAY=2000
CONFIG_ESP_SLEEP_DEEP_SLEEP_WAKEUP_DELAY=2000
CONFIG_ESP32_RTC_CLK_SRC_INT_RC=y
CONFIG_ESP32_RTC_CLOCK_SOURCE_INTERNAL_RC=y
# CONFIG_ESP32_RTC_CLK_SRC_EXT_CRYS is not set
# CONFIG_ESP32_RTC_CLOCK_SOURCE_EXTERNAL_CRYSTAL is not set
# CONFIG_ESP32_RTC_CLK_SRC_EXT_OSC is not set
# CONFIG_ESP32_RTC_CLOCK_SOURCE_EXTERNAL_OSC is not set
# CONFIG_ESP32_RTC_CLK_SRC_INT_8MD256 is not set
# CONFIG_ESP32_RTC_CLOCK_SOURCE_INTERNAL_8MD256 is not set
CONFIG_ESP32_RTC_CLK_CAL_CYCLES=1024
# CONFIG_ESP32_XTAL_FREQ_26 is not set
CONFIG_ESP32_XTAL_FREQ_40=y
# CONFIG_ESP32_XTAL_FREQ_AUTO is not set
CONFIG_ESP32_XTAL_FREQ=40
CONFIG_ESP32_PHY_CALIBRATION_AND_DATA_STORAGE=y
# default:
# CONFIG_ESP32_PHY_INIT_DATA_IN_PARTITION is not set
CONFIG_ESP32_PHY_MAX_WIFI_TX_POWER=20
CONFIG_ESP32_PHY_MAX_TX_POWER=20
CONFIG_REDUCE_PHY_TX_POWER=y
CONFIG_ESP32_REDUCE_PHY_TX_POWER=y
CONFIG_SPIRAM_SUPPORT=y
CONFIG_ESP32_SPIRAM_SUPPORT=y
# default:
# CONFIG_WIFI_LWIP_ALLOCATION_FROM_SPIRAM_FIRST is not set
# CONFIG_ESP32_DEFAULT_CPU_FREQ_80 is not set
# CONFIG_ESP32_DEFAULT_CPU_FREQ_160 is not set
CONFIG_ESP32_DEFAULT_CPU_FREQ_240=y
CONFIG_ESP32_DEFAULT_CPU_FREQ_MHZ=240
CONFIG_TRACEMEM_RESERVE_DRAM=0x0
# CONFIG_ESP32_PANIC_PRINT_HALT is not set
CONFIG_ESP32_PANIC_PRINT_REBOOT=y
# CONFIG_ESP32_PANIC_SILENT_REBOOT is not set
# CONFIG_ESP32_PANIC_GDBSTUB is not set
CONFIG_SYSTEM_EVENT_QUEUE_SIZE=32
CONFIG_SYSTEM_EVENT_TASK_STACK_SIZE=4096
CONFIG_MAIN_TASK_STACK_SIZE=8192
CONFIG_CONSOLE_UART_DEFAULT=y
# CONFIG_CONSOLE_UART_CUSTOM is not set
# CONFIG_CONSOLE_UART_NONE is not set
# CONFIG_ESP_CONSOLE_UART_NONE is not set
CONFIG_CONSOLE_UART=y
CONFIG_CONSOLE_UART_NUM=0
CONFIG_CONSOLE_UART_BAUDRATE=115200
CONFIG_INT_WDT=y
CONFIG_INT_WDT_TIMEOUT_MS=300
CONFIG_INT_WDT_CHECK_CPU1=y
CONFIG_TASK_WDT=y
CONFIG_ESP_TASK_WDT=y
# default:
# CONFIG_TASK_WDT_PANIC is not set
CONFIG_TASK_WDT_TIMEOUT_S=5
CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU0=y
CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU1=y
# default:
# CONFIG_ESP32_DEBUG_STUBS_ENABLE is not set
CONFIG_ESP32_DEBUG_OCDAWARE=y
CONFIG_BROWNOUT_DET=y
CONFIG_ESP32_BROWNOUT_DET=y
CONFIG_BROWNOUT_DET_LVL_SEL_0=y
CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_0=y
# CONFIG_BROWNOUT_DET_LVL_SEL_1 is not set
# CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_1 is not set
# CONFIG_BROWNOUT_DET_LVL_SEL_2 is not set
# CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_2 is not set
# CONFIG_BROWNOUT_DET_LVL_SEL_3 is not set
# CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_3 is not set
# CONFIG_BROWNOUT_DET_LVL_SEL_4 is not set
# CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_4 is not set
# CONFIG_BROWNOUT_DET_LVL_SEL_5 is not set
# CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_5 is not set
# CONFIG_BROWNOUT_DET_LVL_SEL_6 is not set
# CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_6 is not set
# CONFIG_BROWNOUT_DET_LVL_SEL_7 is not set
# CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_7 is not set
CONFIG_BROWNOUT_DET_LVL=0
CONFIG_ESP32_BROWNOUT_DET_LVL=0
# default:
# CONFIG_DISABLE_BASIC_ROM_CONSOLE is not set
CONFIG_IPC_TASK_STACK_SIZE=1536
CONFIG_TIMER_TASK_STACK_SIZE=3584
CONFIG_ESP32_WIFI_ENABLED=y
CONFIG_ESP32_WIFI_STATIC_RX_BUFFER_NUM=10
CONFIG_ESP32_WIFI_DYNAMIC_RX_BUFFER_NUM=32
CONFIG_ESP32_WIFI_STATIC_TX_BUFFER=y
# CONFIG_ESP32_WIFI_DYNAMIC_TX_BUFFER is not set
CONFIG_ESP32_WIFI_TX_BUFFER_TYPE=0
CONFIG_ESP32_WIFI_STATIC_TX_BUFFER_NUM=16
CONFIG_ESP32_WIFI_CACHE_TX_BUFFER_NUM=32
# default:
# CONFIG_ESP32_WIFI_CSI_ENABLED is not set
CONFIG_ESP32_WIFI_AMPDU_TX_ENABLED=y
CONFIG_ESP32_WIFI_TX_BA_WIN=6
CONFIG_ESP32_WIFI_AMPDU_RX_ENABLED=y
CONFIG_ESP32_WIFI_AMPDU_RX_ENABLED=y
CONFIG_ESP32_WIFI_RX_BA_WIN=6
CONFIG_ESP32_WIFI_RX_BA_WIN=6
# default:
# CONFIG_ESP32_WIFI_AMSDU_TX_ENABLED is not set
CONFIG_ESP32_WIFI_NVS_ENABLED=y
CONFIG_ESP32_WIFI_TASK_PINNED_TO_CORE_0=y
# CONFIG_ESP32_WIFI_TASK_PINNED_TO_CORE_1 is not set
CONFIG_ESP32_WIFI_SOFTAP_BEACON_MAX_LEN=752
CONFIG_ESP32_WIFI_MGMT_SBUF_NUM=32
CONFIG_ESP32_WIFI_IRAM_OPT=y
CONFIG_ESP32_WIFI_RX_IRAM_OPT=y
CONFIG_ESP32_WIFI_ENABLE_WPA3_SAE=y
CONFIG_ESP32_WIFI_ENABLE_WPA3_OWE_STA=y
CONFIG_WPA_MBEDTLS_CRYPTO=y
CONFIG_WPA_MBEDTLS_TLS_CLIENT=y
# default:
# CONFIG_WPA_WAPI_PSK is not set
# default:
# CONFIG_WPA_11KV_SUPPORT is not set
# default:
# CONFIG_WPA_MBO_SUPPORT is not set
# default:
# CONFIG_WPA_DPP_SUPPORT is not set
# default:
# CONFIG_WPA_11R_SUPPORT is not set
# default:
# CONFIG_WPA_WPS_SOFTAP_REGISTRAR is not set
# default:
# CONFIG_WPA_WPS_STRICT is not set
# default:
# CONFIG_WPA_DEBUG_PRINT is not set
# default:
# CONFIG_WPA_TESTING_OPTIONS is not set
# CONFIG_ESP32_ENABLE_COREDUMP_TO_FLASH is not set
# CONFIG_ESP32_ENABLE_COREDUMP_TO_UART is not set
CONFIG_ESP32_ENABLE_COREDUMP_TO_NONE=y
CONFIG_TIMER_TASK_PRIORITY=1
CONFIG_TIMER_TASK_STACK_DEPTH=2048
CONFIG_TIMER_QUEUE_LENGTH=10
# default:
# CONFIG_ENABLE_STATIC_TASK_CLEAN_UP_HOOK is not set
# default:
# CONFIG_L2_TO_L3_COPY is not set
CONFIG_ESP_GRATUITOUS_ARP=y
CONFIG_GARP_TMR_INTERVAL=60
CONFIG_TCPIP_RECVMBOX_SIZE=32
CONFIG_TCP_MAXRTX=12
CONFIG_TCP_SYNMAXRTX=12
CONFIG_TCP_MSS=1440
CONFIG_TCP_MSL=60000
CONFIG_TCP_SND_BUF_DEFAULT=5744
CONFIG_TCP_WND_DEFAULT=5744
CONFIG_TCP_RECVMBOX_SIZE=6
CONFIG_TCP_QUEUE_OOSEQ=y
CONFIG_TCP_OVERSIZE_MSS=y
# CONFIG_TCP_OVERSIZE_QUARTER_MSS is not set
# CONFIG_TCP_OVERSIZE_DISABLE is not set
CONFIG_UDP_RECVMBOX_SIZE=6
CONFIG_TCPIP_TASK_STACK_SIZE=3072
CONFIG_TCPIP_TASK_AFFINITY_NO_AFFINITY=y
# CONFIG_TCPIP_TASK_AFFINITY_CPU0 is not set
# CONFIG_TCPIP_TASK_AFFINITY_CPU1 is not set
CONFIG_TCPIP_TASK_AFFINITY=0x7FFFFFFF
# default:
# CONFIG_PPP_SUPPORT is not set
CONFIG_ESP32_TIME_SYSCALL_USE_RTC_HRT=y
CONFIG_ESP32_TIME_SYSCALL_USE_RTC_FRC1=y
# CONFIG_ESP32_TIME_SYSCALL_USE_RTC is not set
# CONFIG_ESP32_TIME_SYSCALL_USE_HRT is not set
# CONFIG_ESP32_TIME_SYSCALL_USE_FRC1 is not set
# CONFIG_ESP32_TIME_SYSCALL_USE_NONE is not set
CONFIG_ESP32_PTHREAD_TASK_PRIO_DEFAULT=5
CONFIG_ESP32_PTHREAD_TASK_STACK_SIZE_DEFAULT=3072
CONFIG_ESP32_PTHREAD_STACK_MIN=768
CONFIG_ESP32_DEFAULT_PTHREAD_CORE_NO_AFFINITY=y
# CONFIG_ESP32_DEFAULT_PTHREAD_CORE_0 is not set
# CONFIG_ESP32_DEFAULT_PTHREAD_CORE_1 is not set
CONFIG_ESP32_PTHREAD_TASK_CORE_DEFAULT=-1
CONFIG_ESP32_PTHREAD_TASK_NAME_DEFAULT="pthread"
CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ABORTS=y
# CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_FAILS is not set
# CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ALLOWED is not set
# default:
# CONFIG_ESP32_ULP_COPROC_ENABLED is not set
CONFIG_SUPPRESS_SELECT_DEBUG_OUTPUT=y
CONFIG_SUPPORT_TERMIOS=y
CONFIG_SEMIHOSTFS_MAX_MOUNT_POINTS=1
# End of deprecated options
