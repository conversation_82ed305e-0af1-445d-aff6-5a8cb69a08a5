/**
 * @file streams-url_mp3-out.ino
 * <AUTHOR>
 * @brief decode MP3 stream from url and output it on I2S
 * @version 0.1
 * @date 2021-96-25
 * 
 * @copyright Copyright (c) 2021
 */

// install https://github.com/pscha<PERSON>mann/arduino-libhelix.git

#include "AudioTools.h"
#include "AudioTools/AudioCodecs/CodecMP3Helix.h"


URLStream url("ssid","password");
AnalogAudioStream out; // final output of decoded stream
EncodedAudioStream dec(&out, new MP3DecoderHelix()); // Decoding stream
StreamCopy copier(dec, url); // copy url to decoder


void setup(){
  Serial.begin(115200);
  AudioToolsLogger.begin(Serial, AudioToolsLogLevel::Info);  

  // setup out
  auto config = out.defaultConfig(TX_MODE);
  out.begin(config);

  // setup I2S based on sampling rate provided by decoder
  dec.begin();

// mp3 radio
  url.begin("http://stream.srg-ssr.ch/m/rsj/mp3_128","audio/mp3");

}

void loop(){
  copier.copy();
}
