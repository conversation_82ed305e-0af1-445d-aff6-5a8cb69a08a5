/* ------------------------------------------------------------
author: "Guitarix project (http://guitarix.sourceforge.net/)"
copyright: "Guitarix project"
license: "LGPL"
name: "guitarix"
version: "0.29"
Code generated with Faust 2.40.12 (https://faust.grame.fr)
Compilation options: -lang cpp -es 1 -mcd 16 -single -ftz 0
------------------------------------------------------------ */

#ifndef  __mydsp_H__
#define  __mydsp_H__

#ifndef FAUSTFLOAT
#define FAUSTFLOAT float
#endif 

#include <algorithm>
#include <cmath>
#include <cstdint>
#include <math.h>

#ifndef FAUSTCLASS 
#define FAUSTCLASS mydsp
#endif

#ifdef __APPLE__ 
#define exp10f __exp10f
#define exp10 __exp10
#endif

#if defined(_WIN32)
#define RESTRICT __restrict
#else
#define RESTRICT __restrict__
#endif

static const float fmydspSIG0Wave0[2001] = {249.987076f,249.986847f,249.986633f,249.986404f,249.986176f,249.985947f,249.985703f,249.985458f,249.985214f,249.98497f,249.984711f,249.984451f,249.984192f,249.983932f,249.983658f,249.983383f,249.983109f,249.982819f,249.982529f,249.982239f,249.981934f,249.981628f,249.981323f,249.981003f,249.980682f,249.980362f,249.980026f,249.979691f,249.979355f,249.979004f,249.978653f,249.978287f,249.977921f,249.977554f,249.977173f,249.976791f,249.976395f,249.975998f,249.975601f,249.975189f,249.974762f,249.97435f,249.973907f,249.97348f,249.973022f,249.972565f,249.972107f,249.971634f,249.971161f,249.970673f,249.970184f,249.969681f,249.969177f,249.968658f,249.968124f,249.96759f,249.967041f,249.966492f,249.965927f,249.965347f,249.964767f,249.964172f,249.963577f,249.962967f,249.962341f,249.961716f,249.96106f,249.960403f,249.959747f,249.959061f,249.958374f,249.957687f,249.95697f,249.956253f,249.955505f,249.954758f,249.95401f,249.953232f,249.952438f,249.951645f,249.950836f,249.950012f,249.949173f,249.948318f,249.947449f,249.946564f,249.945663f,249.944763f,249.943832f,249.942886f,249.941925f,249.940964f,249.939972f,249.938965f,249.937943f,249.93689f,249.935837f,249.934753f,249.93367f,249.932556f,249.931427f,249.930283f,249.929108f,249.927917f,249.926712f,249.925491f,249.92424f,249.922974f,249.921677f,249.920364f,249.919037f,249.917679f,249.916306f,249.914902f,249.913483f,249.912033f,249.910553f,249.909058f,249.907547f,249.905991f,249.904419f,249.902832f,249.901199f,249.899551f,249.897873f,249.896164f,249.89444f,249.89267f,249.890884f,249.889053f,249.887207f,249.88533f,249.883408f,249.88147f,249.879486f,249.877487f,249.875443f,249.873367f,249.871262f,249.86911f,249.866928f,249.864716f,249.862457f,249.860168f,249.857849f,249.855484f,249.853073f,249.850632f,249.848145f,249.845627f,249.843048f,249.840454f,249.837799f,249.835098f,249.832367f,249.829575f,249.826752f,249.823883f,249.820953f,249.817993f,249.814972f,249.811905f,249.808777f,249.805618f,249.802399f,249.799118f,249.795792f,249.792419f,249.788986f,249.785492f,249.781952f,249.778336f,249.774673f,249.77095f,249.767166f,249.763321f,249.759415f,249.755447f,249.751419f,249.747314f,249.743149f,249.738922f,249.734619f,249.730255f,249.725815f,249.721298f,249.716721f,249.712051f,249.707321f,249.702515f,249.697632f,249.692673f,249.687622f,249.682495f,249.677292f,249.671997f,249.666626f,249.661163f,249.655624f,249.649994f,249.644257f,249.638443f,249.632538f,249.626541f,249.620438f,249.614243f,249.607956f,249.601562f,249.595062f,249.58847f,249.581772f,249.574966f,249.568054f,249.561035f,249.553909f,249.546661f,249.539307f,249.53183f,249.524246f,249.516541f,249.508698f,249.500748f,249.492676f,249.484482f,249.476151f,249.467697f,249.459106f,249.450378f,249.441528f,249.432526f,249.423401f,249.414124f,249.404709f,249.395142f,249.385437f,249.375565f,249.365555f,249.355392f,249.345078f,249.334595f,249.323959f,249.313171f,249.3022f,249.291077f,249.27977f,249.268311f,249.256668f,249.244843f,249.232849f,249.220673f,249.208298f,249.195755f,249.183014f,249.17009f,249.156967f,249.143661f,249.130142f,249.116425f,249.102509f,249.088379f,249.074051f,249.059494f,249.044739f,249.029755f,249.014557f,248.99913f,248.98349f,248.967606f,248.951508f,248.935165f,248.918579f,248.901749f,248.884689f,248.867371f,248.849808f,248.832001f,248.813919f,248.795593f,248.776993f,248.758133f,248.739014f,248.719604f,248.699936f,248.679977f,248.659744f,248.639221f,248.618408f,248.597305f,248.575912f,248.554214f,248.532211f,248.509903f,248.487289f,248.464355f,248.441116f,248.417557f,248.393661f,248.369446f,248.344894f,248.320023f,248.2948f,248.269226f,248.243317f,248.217072f,248.19046f,248.163483f,248.136154f,248.108459f,248.080399f,248.051956f,248.023148f,247.993958f,247.964386f,247.934418f,247.904068f,247.873306f,247.842163f,247.810608f,247.778641f,247.746262f,247.71347f,247.680267f,247.646622f,247.612564f,247.578064f,247.543137f,247.507767f,247.471954f,247.435699f,247.398987f,247.361816f,247.324173f,247.286087f,247.247513f,247.208481f,247.168976f,247.128983f,247.088501f,247.047531f,247.006073f,246.964111f,246.921661f,246.878693f,246.83522f,246.791229f,246.746719f,246.701706f,246.656143f,246.610062f,246.563446f,246.516296f,246.468613f,246.420364f,246.371582f,246.322235f,246.272324f,246.221863f,246.170837f,246.119247f,246.067078f,246.014328f,245.960999f,245.907074f,245.85257f,245.79747f,245.741776f,245.685486f,245.628586f,245.571091f,245.51297f,245.454239f,245.394882f,245.334915f,245.274307f,245.213089f,245.151215f,245.088715f,245.025574f,244.961792f,244.897369f,244.832291f,244.766556f,244.70015f,244.633102f,244.565384f,244.496994f,244.427948f,244.358215f,244.287811f,244.216721f,244.144958f,244.07251f,243.999359f,243.925522f,243.850998f,243.775772f,243.699844f,243.623199f,243.545868f,243.467819f,243.389053f,243.309586f,243.229401f,243.148483f,243.066849f,242.984497f,242.901413f,242.817596f,242.733047f,242.647781f,242.561752f,242.475006f,242.387512f,242.299271f,242.210281f,242.12056f,242.030075f,241.938843f,241.846863f,241.75412f,241.660629f,241.566376f,241.471359f,241.37558f,241.279037f,241.181732f,241.083664f,240.984818f,240.885208f,240.784821f,240.68367f,240.581741f,240.479034f,240.375549f,240.271286f,240.166245f,240.060425f,239.953827f,239.846451f,239.738281f,239.629333f,239.519592f,239.409073f,239.29776f,239.185654f,239.072769f,238.959091f,238.84462f,238.72937f,238.613327f,238.496475f,238.378845f,238.260422f,238.141205f,238.021194f,237.900391f,237.778778f,237.656387f,237.533203f,237.40921f,237.284439f,237.158859f,237.032486f,236.905334f,236.777374f,236.648605f,236.519058f,236.388718f,236.257568f,236.125641f,235.992905f,235.859375f,235.725067f,235.589951f,235.454041f,235.317337f,235.179855f,235.041565f,234.902481f,234.762619f,234.621964f,234.480515f,234.338272f,234.195251f,234.051422f,233.90683f,233.761429f,233.61525f,233.468292f,233.320541f,233.172012f,233.022705f,232.872604f,232.721725f,232.570068f,232.417618f,232.264404f,232.110413f,231.955643f,231.800095f,231.643768f,231.486679f,231.328812f,231.170166f,231.010757f,230.850571f,230.689621f,230.527908f,230.365433f,230.202194f,230.038177f,229.873413f,229.707886f,229.541595f,229.374542f,229.206741f,229.038177f,228.868866f,228.698807f,228.527985f,228.35643f,228.184113f,228.011047f,227.837234f,227.662689f,227.487396f,227.311356f,227.134583f,226.957077f,226.778824f,226.599838f,226.42012f,226.23967f,226.058502f,225.876587f,225.693954f,225.51059f,225.326508f,225.141708f,224.956177f,224.769928f,224.582962f,224.395279f,224.206894f,224.017776f,223.827972f,223.637436f,223.446198f,223.254272f,223.061615f,222.868271f,222.674225f,222.479477f,222.284042f,222.087906f,221.891068f,221.693542f,221.495331f,221.296432f,221.096848f,220.896576f,220.695618f,220.493973f,220.291656f,220.088654f,219.884979f,219.680634f,219.475616f,219.269928f,219.063568f,218.856537f,218.648849f,218.440491f,218.231476f,218.021805f,217.811462f,217.600479f,217.388824f,217.176529f,216.963577f,216.749969f,216.535721f,216.320831f,216.105301f,215.889114f,215.672302f,215.454834f,215.23674f,215.018021f,214.798645f,214.578659f,214.358032f,214.13678f,213.914902f,213.692398f,213.469284f,213.245529f,213.021164f,212.796188f,212.570602f,212.344391f,212.117569f,211.890137f,211.662094f,211.433456f,211.204208f,210.97435f,210.743912f,210.512848f,210.281204f,210.048965f,209.816132f,209.582687f,209.348679f,209.114059f,208.878876f,208.643097f,208.406723f,208.169785f,207.932266f,207.694153f,207.455475f,207.216232f,206.976395f,206.736008f,206.495041f,206.25351f,206.011414f,205.768738f,205.525513f,205.281738f,205.037384f,204.79248f,204.547028f,204.301025f,204.054459f,203.807343f,203.559677f,203.311478f,203.062714f,202.813416f,202.563583f,202.313202f,202.062286f,201.810837f,201.558838f,201.30632f,201.053268f,200.799683f,200.545578f,200.290939f,200.035767f,199.780075f,199.52388f,199.267151f,199.009903f,198.752136f,198.493851f,198.235062f,197.975769f,197.715958f,197.455627f,197.194809f,196.933472f,196.671631f,196.409302f,196.146454f,195.883118f,195.619293f,195.354965f,195.090134f,194.824829f,194.559021f,194.292725f,194.025955f,193.758682f,193.490936f,193.222702f,192.953995f,192.684814f,192.415146f,192.145004f,191.87439f,191.603302f,191.331741f,191.059723f,190.787231f,190.514267f,190.240845f,189.966965f,189.692627f,189.417816f,189.142563f,188.866837f,188.590668f,188.314056f,188.036987f,187.75946f,187.481491f,187.203079f,186.924225f,186.644928f,186.365204f,186.085022f,185.804413f,185.523361f,185.241882f,184.959976f,184.677643f,184.394867f,184.111679f,183.828064f,183.544022f,183.259567f,182.974701f,182.689407f,182.403702f,182.117569f,181.831039f,181.544113f,181.25676f,180.969009f,180.680862f,180.392303f,180.103348f,179.814011f,179.524261f,179.234131f,178.943604f,178.652695f,178.361389f,178.069702f,177.777649f,177.485199f,177.192383f,176.8992f,176.605637f,176.311691f,176.017395f,175.722733f,175.427704f,175.132324f,174.836594f,174.540497f,174.244049f,173.947266f,173.650131f,173.352646f,173.054825f,172.756683f,172.458191f,172.159378f,171.860245f,171.560776f,171.261002f,170.960907f,170.660492f,170.359772f,170.058746f,169.757431f,169.455811f,169.1539f,168.8517f,168.549225f,168.24646f,167.94342f,167.640121f,167.336548f,167.032715f,166.728622f,166.424286f,166.11969f,165.814865f,165.509811f,165.204529f,164.899017f,164.593292f,164.287369f,163.981232f,163.674896f,163.368378f,163.061676f,162.754791f,162.447739f,162.140533f,161.833176f,161.525681f,161.218033f,160.910278f,160.602386f,160.294403f,159.986313f,159.678131f,159.369873f,159.061554f,158.753174f,158.444733f,158.136276f,157.827789f,157.519287f,157.210785f,156.902283f,156.593826f,156.2854f,155.977036f,155.668732f,155.360504f,155.052383f,154.74437f,154.436478f,154.128738f,153.821167f,153.513763f,153.206558f,152.899551f,152.592789f,152.286285f,151.980042f,151.674088f,151.368439f,151.063126f,150.758163f,150.453568f,150.149384f,149.845596f,149.542267f,149.23938f,148.936996f,148.635117f,148.333786f,148.033005f,147.732803f,147.433228f,147.134277f,146.835999f,146.538422f,146.241547f,145.945435f,145.650085f,145.355545f,145.061829f,144.768967f,144.477005f,144.185959f,143.895859f,143.606735f,143.318604f,143.031525f,142.745499f,142.460556f,142.176743f,141.894089f,141.61261f,141.332336f,141.053299f,140.775528f,140.499039f,140.223892f,139.950073f,139.677643f,139.406601f,139.136993f,138.868835f,138.602158f,138.33699f,138.073334f,137.811234f,137.55069f,137.291748f,137.034424f,136.778717f,136.524673f,136.272293f,136.021606f,135.772614f,135.525345f,135.279816f,135.036026f,134.794006f,134.553741f,134.315277f,134.078598f,133.843735f,133.610672f,133.379425f,133.150009f,132.922424f,132.696671f,132.472748f,132.250671f,132.030441f,131.812057f,131.595505f,131.380798f,131.167938f,130.956909f,130.747726f,130.540359f,130.334824f,130.131104f,129.929214f,129.729111f,129.530823f,129.33432f,129.139603f,128.946671f,128.755493f,128.566071f,128.378387f,128.192429f,128.008209f,127.825691f,127.644867f,127.465729f,127.288269f,127.112457f,126.938293f,126.765755f,126.594841f,126.425522f,126.25779f,126.091629f,125.927032f,125.763977f,125.602448f,125.442429f,125.283905f,125.126862f,124.971291f,124.817169f,124.664482f,124.513214f,124.36335f,124.214874f,124.067772f,123.922028f,123.777626f,123.634544f,123.492783f,123.35231f,123.213127f,123.075203f,122.938538f,122.803101f,122.668884f,122.535881f,122.404068f,122.27343f,122.143951f,122.015633f,121.888435f,121.762367f,121.637405f,121.513535f,121.390747f,121.26902f,121.148354f,121.028725f,120.910118f,120.792526f,120.675941f,120.560341f,120.445717f,120.332054f,120.219353f,120.107582f,119.996742f,119.886818f,119.777802f,119.669678f,119.562439f,119.45607f,119.350563f,119.245903f,119.142082f,119.039093f,118.93692f,118.835556f,118.734993f,118.635216f,118.536217f,118.437988f,118.340515f,118.243797f,118.14782f,118.052574f,117.958046f,117.864235f,117.771133f,117.678719f,117.586998f,117.495956f,117.405594f,117.315887f,117.226837f,117.138435f,117.050667f,116.963539f,116.877037f,116.791145f,116.705864f,116.621193f,116.537109f,116.453621f,116.370712f,116.288376f,116.206612f,116.125404f,116.044762f,115.964661f,115.885101f,115.806076f,115.727585f,115.64962f,115.572174f,115.495239f,115.418808f,115.34288f,115.267441f,115.192497f,115.118042f,115.04406f,114.970551f,114.897514f,114.824936f,114.752823f,114.681152f,114.60994f,114.539169f,114.468834f,114.398933f,114.32946f,114.260414f,114.191788f,114.123573f,114.055779f,113.98838f,113.921394f,113.854805f,113.788605f,113.722794f,113.657372f,113.592339f,113.527672f,113.463387f,113.399467f,113.335922f,113.272736f,113.209908f,113.147438f,113.085312f,113.023544f,112.96212f,112.901031f,112.840286f,112.779877f,112.719795f,112.660042f,112.600616f,112.541519f,112.482735f,112.424263f,112.366104f,112.308258f,112.250717f,112.193481f,112.136551f,112.07991f,112.023575f,111.967522f,111.911766f,111.856293f,111.801109f,111.746201f,111.691574f,111.63723f,111.583153f,111.52935f,111.475815f,111.422546f,111.369545f,111.316803f,111.26432f,111.212097f,111.160133f,111.108414f,111.056946f,111.00573f,110.954758f,110.90403f,110.853546f,110.803299f,110.753296f,110.703522f,110.653984f,110.604675f,110.555595f,110.506744f,110.458122f,110.409721f,110.361542f,110.313583f,110.265839f,110.218315f,110.171005f,110.123909f,110.077026f,110.03035f,109.983879f,109.937614f,109.891556f,109.845703f,109.800041f,109.754585f,109.709328f,109.664268f,109.6194f,109.574722f,109.530243f,109.485947f,109.441841f,109.397919f,109.354187f,109.310638f,109.267273f,109.224091f,109.181084f,109.138252f,109.095604f,109.053131f,109.010826f,108.968704f,108.926743f,108.884956f,108.843346f,108.801895f,108.760612f,108.719498f,108.678543f,108.637756f,108.597122f,108.556656f,108.51635f,108.476196f,108.436203f,108.39637f,108.356682f,108.317154f,108.277779f,108.238548f,108.199478f,108.160553f,108.121773f,108.083138f,108.044655f,108.006317f,107.968117f,107.930061f,107.892151f,107.854385f,107.81675f,107.779259f,107.741905f,107.704689f,107.66761f,107.630669f,107.593857f,107.557175f,107.52063f,107.484215f,107.447937f,107.411781f,107.375755f,107.339859f,107.304092f,107.268448f,107.232933f,107.19754f,107.16227f,107.127129f,107.092102f,107.057198f,107.022423f,106.987755f,106.953217f,106.918793f,106.884483f,106.850296f,106.816223f,106.782265f,106.748421f,106.714684f,106.681068f,106.647568f,106.614174f,106.580887f,106.547714f,106.514656f,106.481705f,106.448853f,106.416115f,106.383484f,106.35096f,106.318542f,106.286232f,106.254021f,106.221916f,106.189911f,106.158005f,106.126205f,106.094513f,106.062912f,106.031418f,106.000015f,105.968719f,105.937515f,105.90641f,105.875404f,105.84449f,105.813675f,105.782959f,105.752335f,105.721802f,105.691368f,105.661018f,105.630768f,105.600609f,105.570541f,105.540565f,105.510681f,105.480881f,105.451172f,105.421555f,105.392021f,105.362579f,105.333229f,105.303955f,105.274773f,105.245682f,105.216667f,105.187744f,105.158905f,105.130142f,105.101471f,105.072876f,105.044373f,105.015945f,104.987602f,104.959335f,104.931152f,104.903053f,104.875031f,104.847084f,104.819221f,104.791435f,104.763733f,104.736099f,104.708549f,104.681076f,104.653679f,104.626358f,104.599106f,104.571938f,104.544838f,104.517822f,104.490868f,104.463997f,104.437195f,104.410469f,104.383812f,104.357231f,104.330719f,104.304283f,104.277916f,104.251617f,104.225388f,104.199234f,104.173141f,104.147125f,104.121178f,104.095299f,104.069481f,104.043739f,104.018059f,103.992455f,103.966911f,103.941437f,103.916023f,103.890678f,103.865402f,103.840187f,103.815041f,103.789955f,103.764938f,103.739983f,103.715088f,103.690262f,103.665497f,103.640793f,103.61615f,103.591568f,103.567055f,103.542603f,103.518204f,103.493874f,103.469597f,103.445389f,103.421234f,103.397141f,103.373108f,103.349136f,103.325226f,103.301369f,103.277573f,103.25383f,103.230148f,103.20652f,103.182953f,103.159447f,103.135994f,103.112595f,103.089256f,103.065971f,103.04274f,103.019569f,102.996452f,102.973389f,102.950378f,102.927422f,102.904526f,102.881676f,102.858887f,102.836143f,102.813461f,102.790825f,102.76825f,102.74572f,102.723244f,102.700821f,102.678452f,102.656128f,102.633858f,102.611641f,102.589478f,102.56736f,102.545296f,102.523277f,102.501312f,102.479401f,102.457535f,102.435715f,102.413948f,102.392227f,102.37056f,102.348938f,102.327362f,102.30584f,102.284363f,102.262932f,102.241547f,102.220215f,102.198929f,102.177689f,102.156494f,102.135345f,102.114243f,102.093185f,102.072174f,102.051208f,102.030296f,102.009422f,101.988594f,101.967804f,101.947067f,101.926376f,101.905724f,101.885117f,101.864555f,101.84404f,101.823563f,101.803131f,101.782745f,101.762398f,101.742096f,101.72184f,101.701622f,101.68145f,101.661316f,101.641228f,101.621178f,101.601173f,101.581207f,101.561287f,101.541405f,101.521561f,101.501762f,101.482002f,101.462288f,101.442604f,101.422966f,101.403374f,101.383812f,101.364296f,101.344818f,101.325378f,101.305977f,101.286613f,101.267288f,101.248009f,101.228767f,101.209557f,101.190392f,101.171257f,101.152168f,101.133118f,101.114098f,101.095123f,101.07618f,101.057281f,101.038414f,101.019585f,101.000793f,100.982033f,100.963318f,100.944633f,100.925987f,100.907379f,100.888809f,100.87027f,100.851768f,100.833305f,100.814873f,100.796478f,100.778114f,100.759796f,100.741501f,100.723251f,100.705032f,100.686844f,100.668694f,100.650574f,100.632492f,100.614449f,100.596436f,100.578453f,100.560509f,100.542595f,100.524719f,100.506866f,100.489059f,100.471275f,100.453529f,100.435822f,100.418137f,100.40049f,100.382874f,100.365295f,100.34774f,100.330223f,100.312737f,100.29528f,100.277863f,100.260468f,100.243111f,100.225784f,100.208488f,100.191223f,100.173988f,100.156784f,100.139618f,100.122475f,100.10537f,100.088287f,100.071236f,100.054222f,100.037231f,100.020271f,100.003349f,99.9864502f,99.9695816f,99.9527435f,99.935936f,99.9191589f,99.9024124f,99.8856888f,99.8689957f,99.8523407f,99.8357086f,99.8191071f,99.8025284f,99.7859879f,99.7694702f,99.7529831f,99.7365189f,99.7200928f,99.7036896f,99.6873169f,99.6709671f,99.6546478f,99.6383591f,99.6220932f,99.6058655f,99.589653f,99.5734787f,99.5573273f,99.5411987f,99.5251007f,99.5090332f,99.4929886f,99.4769745f,99.4609833f,99.4450226f,99.4290848f,99.4131775f,99.3973007f,99.3814392f,99.3656158f,99.3498077f,99.3340378f,99.3182831f,99.3025589f,99.2868652f,99.2711868f,99.2555466f,99.2399216f,99.2243271f,99.2087631f,99.1932144f,99.1776962f,99.1622009f,99.1467361f,99.1312943f,99.1158752f,99.1004868f,99.0851135f,99.0697708f,99.0544586f,99.0391617f,99.0238953f,99.0086517f,98.9934311f,98.9782333f,98.9630661f,98.9479218f,98.9327927f,98.9176941f,98.902626f,98.8875732f,98.8725433f,98.8575439f,98.8425674f,98.8276062f,98.8126755f,98.7977676f,98.7828827f,98.7680206f,98.7531891f,98.7383728f,98.7235794f,98.7088089f,98.6940613f,98.6793442f,98.6646423f,98.6499634f,98.6353073f,98.6206818f,98.6060715f,98.5914841f,98.5769196f,98.5623779f,98.5478592f,98.5333633f,98.5188828f,98.5044327f,98.4900055f,98.4755936f,98.4612045f,98.4468384f,98.4324951f,98.4181747f,98.4038773f,98.389595f,98.3753433f,98.3611069f,98.3468933f,98.3327026f,98.3185272f,98.3043823f,98.2902527f,98.2761459f,98.2620544f,98.2479935f,98.2339478f,98.2199249f,98.2059174f,98.1919327f,98.1779709f,98.164032f,98.150116f,98.1362152f,98.1223297f,98.1084747f,98.094635f,98.0808182f,98.0670166f,98.0532379f,98.0394821f,98.0257416f,98.0120239f,97.9983292f,97.9846497f,97.9709854f,97.9573517f,97.9437332f,97.93013f,97.9165497f,97.9029922f,97.8894501f,97.8759308f,97.8624268f,97.8489456f,97.8354797f,97.8220367f,97.808609f,97.7952042f,97.7818146f,97.7684479f,97.7550964f,97.7417679f,97.7284546f,97.7151642f,97.701889f,97.6886368f,97.6753998f,97.662178f,97.6489792f,97.6357956f,97.6226349f,97.6094894f,97.5963669f,97.583252f,97.5701675f,97.5570908f,97.5440445f,97.5310059f,97.5179901f,97.5049896f,97.4920044f,97.4790421f,97.466095f,97.4531708f,97.4402618f,97.4273682f,97.4144897f,97.4016342f,97.3887939f,97.3759766f,97.3631668f,97.3503799f,97.3376083f,97.3248596f,97.3121262f,97.299408f,97.286705f,97.2740173f,97.2613525f,97.248703f,97.2360687f,97.2234573f,97.2108536f,97.1982727f,97.1857071f,97.1731567f,97.1606293f,97.1481094f,97.1356125f,97.1231308f,97.1106644f,97.0982208f,97.0857849f,97.0733719f,97.0609665f,97.048584f,97.0362167f,97.0238647f,97.0115356f,96.9992142f,96.9869156f,96.9746246f,96.9623566f,96.9501038f,96.9378662f,96.9256439f,96.9134369f,96.9012451f,96.8890686f,96.876915f,96.864769f,96.8526382f,96.8405304f,96.8284302f,96.8163528f,96.8042908f,96.7922363f,96.7802048f,96.7681808f,96.7561798f,96.744194f,96.7322235f,96.7202606f,96.7083206f,96.6963959f,96.6844788f,96.6725845f,96.6607056f,96.6488342f,96.6369858f,96.625145f,96.613327f,96.6015167f,96.5897217f,96.5779495f,96.566185f,96.5544357f,96.5427017f,96.530983f,96.5192795f,96.5075912f,96.4959183f,96.4842529f,96.4726105f,96.4609756f,96.4493637f,96.4377594f,96.4261703f,96.4145966f,96.403038f,96.3914948f,96.3799591f,96.3684464f,96.3569412f,96.3454514f,96.3339767f,96.3225174f,96.3110733f,96.2996368f,96.2882156f,96.2768097f,96.265419f,96.2540436f,96.2426834f,96.2313309f,96.2199936f,96.2086716f,96.1973648f,96.1860733f,96.1747894f,96.1635208f,96.1522675f,96.1410294f,96.1297989f,96.1185837f,96.1073837f,96.096199f,96.085022f,96.0738602f,96.0627136f,96.0515823f,96.0404587f,96.0293579f,96.0182571f,96.0071793f,95.996109f,95.985054f,95.9740143f,95.9629822f,95.951973f,95.9409637f,95.9299774f,95.9189987f,95.9080353f,95.8970871f,95.8861465f,95.8752213f,95.8643036f,95.8534088f,95.8425217f,95.8316422f,95.8207779f,95.8099289f,95.7990952f,95.788269f,95.7774582f,95.766655f,95.755867f,95.7450943f,95.7343369f,95.7235794f,95.7128448f,95.7021179f,95.6914062f,95.6807098f,95.6700211f,95.6593399f,95.648674f,95.6380234f,95.627388f,95.6167603f,95.6061401f,95.5955353f,95.5849457f,95.5743637f,95.563797f,95.5532455f,95.5427017f,95.5321655f,95.5216446f,95.5111389f,95.5006409f,95.4901581f,95.4796829f,95.469223f,95.4587708f,95.4483337f,95.437912f,95.4274979f,95.4170914f,95.4067001f,95.3963242f,95.3859558f,95.3755951f,95.3652496f,95.3549194f,95.3445969f,95.3342819f,95.3239822f,95.3136978f,95.303421f,95.2931519f,95.2828979f,95.2726517f,95.2624207f,95.2522049f,95.2419891f,95.2317963f,95.2216034f,95.2114258f,95.2012634f,95.1911087f,95.1809692f,95.1708374f,95.1607132f,95.1506042f,95.1405029f,95.1304169f,95.1203384f,95.1102753f,95.1002197f,95.0901718f,95.0801392f,95.0701141f,95.0601044f,95.0501022f,95.0401077f,95.0301285f,95.0201569f,95.0102005f,95.0002518f,94.9903183f,94.9803848f,94.9704742f,94.9605637f,94.9506683f,94.9407806f,94.9309082f,94.9210434f,94.9111938f,94.9013519f,94.8915176f,94.881691f,94.8718796f,94.8620758f,94.8522873f,94.8425064f,94.8327332f,94.8229752f,94.8132248f,94.8034821f,94.7937546f,94.7840347f,94.7743225f,94.7646179f,94.7549286f,94.7452545f,94.7355804f,94.7259216f,94.7162704f,94.7066345f,94.6970062f,94.6873856f,94.6777725f,94.6681747f,94.6585846f,94.6490021f,94.6394348f,94.6298752f,94.6203232f,94.6107788f,94.6012497f,94.5917282f,94.5822144f,94.5727158f,94.5632172f,94.5537415f,94.5442657f,94.5347977f,94.5253448f,94.5158997f,94.5064697f,94.4970398f,94.4876251f,94.4782181f,94.4688263f,94.4594345f,94.450058f,94.4406891f,94.4313354f,94.4219818f,94.4126434f,94.4033127f,94.3939896f,94.3846817f,94.3753738f,94.3660812f,94.3567963f,94.3475266f,94.3382568f,94.3290024f,94.3197556f,94.3105164f,94.3012848f,94.2920685f,94.2828598f,94.2736588f,94.2644653f,94.2552795f,94.2461014f,94.2369385f,94.2277832f,94.2186356f,94.2094955f,94.2003708f,94.191246f,94.1821365f,94.1730347f,94.1639404f,94.1548538f,94.1457825f,94.1367111f,94.127655f,94.1186066f,94.1095657f,94.1005325f,94.091507f,94.0824966f,94.0734863f,94.0644913f,94.0555038f,94.046524f,94.0375519f,94.028595f,94.0196381f,94.0106964f,94.0017548f,93.9928284f,93.9839096f,93.9749985f,93.966095f,93.9572067f,93.9483185f,93.9394455f,93.9305725f,93.9217148f,93.9128647f,93.9040222f,93.8951874f,93.8863602f,93.8775406f,93.8687363f,93.8599319f,93.8511429f,93.8423538f,93.83358f,93.8248138f,93.8160553f,93.8073044f,93.7985611f,93.7898254f,93.7810974f,93.772377f,93.7636642f};
class mydspSIG0 {
	
  private:
	
	int fmydspSIG0Wave0_idx;
	
  public:
	
	int getNumInputsmydspSIG0() {
		return 0;
	}
	int getNumOutputsmydspSIG0() {
		return 1;
	}
	
	void instanceInitmydspSIG0(int sample_rate) {
		fmydspSIG0Wave0_idx = 0;
	}
	
	void fillmydspSIG0(int count, float* table) {
		for (int i1 = 0; i1 < count; i1 = i1 + 1) {
			table[i1] = fmydspSIG0Wave0[fmydspSIG0Wave0_idx];
			fmydspSIG0Wave0_idx = (1 + fmydspSIG0Wave0_idx) % 2001;
		}
	}

};

static mydspSIG0* newmydspSIG0() { return (mydspSIG0*)new mydspSIG0(); }
static void deletemydspSIG0(mydspSIG0* dsp) { delete dsp; }

static const float fmydspSIG1Wave0[2001] = {249.987076f,249.986847f,249.986633f,249.986404f,249.986176f,249.985947f,249.985703f,249.985458f,249.985214f,249.98497f,249.984711f,249.984451f,249.984192f,249.983932f,249.983658f,249.983383f,249.983109f,249.982819f,249.982529f,249.982239f,249.981934f,249.981628f,249.981323f,249.981003f,249.980682f,249.980362f,249.980026f,249.979691f,249.979355f,249.979004f,249.978653f,249.978287f,249.977921f,249.977554f,249.977173f,249.976791f,249.976395f,249.975998f,249.975601f,249.975189f,249.974762f,249.97435f,249.973907f,249.97348f,249.973022f,249.972565f,249.972107f,249.971634f,249.971161f,249.970673f,249.970184f,249.969681f,249.969177f,249.968658f,249.968124f,249.96759f,249.967041f,249.966492f,249.965927f,249.965347f,249.964767f,249.964172f,249.963577f,249.962967f,249.962341f,249.961716f,249.96106f,249.960403f,249.959747f,249.959061f,249.958374f,249.957687f,249.95697f,249.956253f,249.955505f,249.954758f,249.95401f,249.953232f,249.952438f,249.951645f,249.950836f,249.950012f,249.949173f,249.948318f,249.947449f,249.946564f,249.945663f,249.944763f,249.943832f,249.942886f,249.941925f,249.940964f,249.939972f,249.938965f,249.937943f,249.93689f,249.935837f,249.934753f,249.93367f,249.932556f,249.931427f,249.930283f,249.929108f,249.927917f,249.926712f,249.925491f,249.92424f,249.922974f,249.921677f,249.920364f,249.919037f,249.917679f,249.916306f,249.914902f,249.913483f,249.912033f,249.910553f,249.909058f,249.907547f,249.905991f,249.904419f,249.902832f,249.901199f,249.899551f,249.897873f,249.896164f,249.89444f,249.89267f,249.890884f,249.889053f,249.887207f,249.88533f,249.883408f,249.88147f,249.879486f,249.877487f,249.875443f,249.873367f,249.871262f,249.86911f,249.866928f,249.864716f,249.862457f,249.860168f,249.857849f,249.855484f,249.853073f,249.850632f,249.848145f,249.845627f,249.843048f,249.840454f,249.837799f,249.835098f,249.832367f,249.829575f,249.826752f,249.823883f,249.820953f,249.817993f,249.814972f,249.811905f,249.808777f,249.805618f,249.802399f,249.799118f,249.795792f,249.792419f,249.788986f,249.785492f,249.781952f,249.778336f,249.774673f,249.77095f,249.767166f,249.763321f,249.759415f,249.755447f,249.751419f,249.747314f,249.743149f,249.738922f,249.734619f,249.730255f,249.725815f,249.721298f,249.716721f,249.712051f,249.707321f,249.702515f,249.697632f,249.692673f,249.687622f,249.682495f,249.677292f,249.671997f,249.666626f,249.661163f,249.655624f,249.649994f,249.644257f,249.638443f,249.632538f,249.626541f,249.620438f,249.614243f,249.607956f,249.601562f,249.595062f,249.58847f,249.581772f,249.574966f,249.568054f,249.561035f,249.553909f,249.546661f,249.539307f,249.53183f,249.524246f,249.516541f,249.508698f,249.500748f,249.492676f,249.484482f,249.476151f,249.467697f,249.459106f,249.450378f,249.441528f,249.432526f,249.423401f,249.414124f,249.404709f,249.395142f,249.385437f,249.375565f,249.365555f,249.355392f,249.345078f,249.334595f,249.323959f,249.313171f,249.3022f,249.291077f,249.27977f,249.268311f,249.256668f,249.244843f,249.232849f,249.220673f,249.208298f,249.195755f,249.183014f,249.17009f,249.156967f,249.143661f,249.130142f,249.116425f,249.102509f,249.088379f,249.074051f,249.059494f,249.044739f,249.029755f,249.014557f,248.99913f,248.98349f,248.967606f,248.951508f,248.935165f,248.918579f,248.901749f,248.884689f,248.867371f,248.849808f,248.832001f,248.813919f,248.795593f,248.776993f,248.758133f,248.739014f,248.719604f,248.699936f,248.679977f,248.659744f,248.639221f,248.618408f,248.597305f,248.575912f,248.554214f,248.532211f,248.509903f,248.487289f,248.464355f,248.441116f,248.417557f,248.393661f,248.369446f,248.344894f,248.320023f,248.2948f,248.269226f,248.243317f,248.217072f,248.19046f,248.163483f,248.136154f,248.108459f,248.080399f,248.051956f,248.023148f,247.993958f,247.964386f,247.934418f,247.904068f,247.873306f,247.842163f,247.810608f,247.778641f,247.746262f,247.71347f,247.680267f,247.646622f,247.612564f,247.578064f,247.543137f,247.507767f,247.471954f,247.435699f,247.398987f,247.361816f,247.324173f,247.286087f,247.247513f,247.208481f,247.168976f,247.128983f,247.088501f,247.047531f,247.006073f,246.964111f,246.921661f,246.878693f,246.83522f,246.791229f,246.746719f,246.701706f,246.656143f,246.610062f,246.563446f,246.516296f,246.468613f,246.420364f,246.371582f,246.322235f,246.272324f,246.221863f,246.170837f,246.119247f,246.067078f,246.014328f,245.960999f,245.907074f,245.85257f,245.79747f,245.741776f,245.685486f,245.628586f,245.571091f,245.51297f,245.454239f,245.394882f,245.334915f,245.274307f,245.213089f,245.151215f,245.088715f,245.025574f,244.961792f,244.897369f,244.832291f,244.766556f,244.70015f,244.633102f,244.565384f,244.496994f,244.427948f,244.358215f,244.287811f,244.216721f,244.144958f,244.07251f,243.999359f,243.925522f,243.850998f,243.775772f,243.699844f,243.623199f,243.545868f,243.467819f,243.389053f,243.309586f,243.229401f,243.148483f,243.066849f,242.984497f,242.901413f,242.817596f,242.733047f,242.647781f,242.561752f,242.475006f,242.387512f,242.299271f,242.210281f,242.12056f,242.030075f,241.938843f,241.846863f,241.75412f,241.660629f,241.566376f,241.471359f,241.37558f,241.279037f,241.181732f,241.083664f,240.984818f,240.885208f,240.784821f,240.68367f,240.581741f,240.479034f,240.375549f,240.271286f,240.166245f,240.060425f,239.953827f,239.846451f,239.738281f,239.629333f,239.519592f,239.409073f,239.29776f,239.185654f,239.072769f,238.959091f,238.84462f,238.72937f,238.613327f,238.496475f,238.378845f,238.260422f,238.141205f,238.021194f,237.900391f,237.778778f,237.656387f,237.533203f,237.40921f,237.284439f,237.158859f,237.032486f,236.905334f,236.777374f,236.648605f,236.519058f,236.388718f,236.257568f,236.125641f,235.992905f,235.859375f,235.725067f,235.589951f,235.454041f,235.317337f,235.179855f,235.041565f,234.902481f,234.762619f,234.621964f,234.480515f,234.338272f,234.195251f,234.051422f,233.90683f,233.761429f,233.61525f,233.468292f,233.320541f,233.172012f,233.022705f,232.872604f,232.721725f,232.570068f,232.417618f,232.264404f,232.110413f,231.955643f,231.800095f,231.643768f,231.486679f,231.328796f,231.170166f,231.010757f,230.850571f,230.689621f,230.527908f,230.365433f,230.202194f,230.038177f,229.873413f,229.707886f,229.541595f,229.374542f,229.206741f,229.038177f,228.868866f,228.698807f,228.527985f,228.356415f,228.184113f,228.011047f,227.837234f,227.662689f,227.487396f,227.311356f,227.134583f,226.957077f,226.778824f,226.599838f,226.42012f,226.23967f,226.058487f,225.876587f,225.693954f,225.51059f,225.326508f,225.141693f,224.956177f,224.769928f,224.582962f,224.395279f,224.206879f,224.017776f,223.827957f,223.637436f,223.446198f,223.254257f,223.061615f,222.868271f,222.674225f,222.479477f,222.284027f,222.087891f,221.891068f,221.693542f,221.495331f,221.296417f,221.096832f,220.896561f,220.695602f,220.493958f,220.291641f,220.088654f,219.884979f,219.680634f,219.475601f,219.269913f,219.063553f,218.856522f,218.648834f,218.440475f,218.231461f,218.02179f,217.811447f,217.600449f,217.388809f,217.176498f,216.963547f,216.749954f,216.535706f,216.320816f,216.10527f,215.889084f,215.672272f,215.454803f,215.23671f,215.017975f,214.798615f,214.578629f,214.358002f,214.136749f,213.914871f,213.692368f,213.469238f,213.245483f,213.021118f,212.796143f,212.570541f,212.34433f,212.117508f,211.890076f,211.662048f,211.433395f,211.204147f,210.974289f,210.743835f,210.512787f,210.281128f,210.048889f,209.81604f,209.582611f,209.348587f,209.113968f,208.878769f,208.64299f,208.406631f,208.169678f,207.932144f,207.694046f,207.455353f,207.216095f,206.976273f,206.73587f,206.494888f,206.253357f,206.011246f,205.768585f,205.525345f,205.281555f,205.037201f,204.792297f,204.546829f,204.300812f,204.054245f,203.807114f,203.559448f,203.311234f,203.062469f,202.813156f,202.563309f,202.312912f,202.061981f,201.810516f,201.558517f,201.305984f,201.052917f,200.799316f,200.545181f,200.290527f,200.035355f,199.779648f,199.523422f,199.266678f,199.009399f,198.751617f,198.493317f,198.234512f,197.975189f,197.715347f,197.455002f,197.194153f,196.932785f,196.670929f,196.408554f,196.145691f,195.882324f,195.618454f,195.354095f,195.089233f,194.823883f,194.558044f,194.291702f,194.024887f,193.757568f,193.489777f,193.221497f,192.952744f,192.683502f,192.413788f,192.143585f,191.87291f,191.601761f,191.330154f,191.05806f,190.785492f,190.512466f,190.238968f,189.965012f,189.690582f,189.415695f,189.14035f,188.864548f,188.588272f,188.311554f,188.034378f,187.75676f,187.478683f,187.20015f,186.921173f,186.641754f,186.361893f,186.081573f,185.800827f,185.519638f,185.238007f,184.955933f,184.673431f,184.390488f,184.107117f,183.823303f,183.539078f,183.25441f,182.96933f,182.683823f,182.397873f,182.111526f,181.824738f,181.537552f,181.249939f,180.961899f,180.673462f,180.384598f,180.095337f,179.805664f,179.515579f,179.225082f,178.934189f,178.642899f,178.351196f,178.059097f,177.766602f,177.473709f,177.18042f,176.886734f,176.592667f,176.298203f,176.003357f,175.708115f,175.412506f,175.116501f,174.820114f,174.523346f,174.226212f,173.928696f,173.630798f,173.332535f,173.033905f,172.734894f,172.435532f,172.135788f,171.835693f,171.535233f,171.234421f,170.933243f,170.631714f,170.329834f,170.027603f,169.725021f,169.422089f,169.11882f,168.815201f,168.511246f,168.20694f,167.902313f,167.597351f,167.292053f,166.98642f,166.680466f,166.374191f,166.067596f,165.760666f,165.45343f,165.145889f,164.838028f,164.529846f,164.221375f,163.912598f,163.603516f,163.294144f,162.984467f,162.6745f,162.364258f,162.053711f,161.742905f,161.431808f,161.120438f,160.808792f,160.496887f,160.184708f,159.872269f,159.559586f,159.246643f,158.933441f,158.62001f,158.306335f,157.992432f,157.678284f,157.363922f,157.049332f,156.734528f,156.41951f,156.104279f,155.788849f,155.473236f,155.157425f,154.841431f,154.525253f,154.208908f,153.89241f,153.575729f,153.258911f,152.941956f,152.624847f,152.307602f,151.99025f,151.672775f,151.355194f,151.037506f,150.719727f,150.401871f,150.083939f,149.765945f,149.447891f,149.129807f,148.811676f,148.493515f,148.175354f,147.857178f,147.539017f,147.220871f,146.902756f,146.584686f,146.266678f,145.94873f,145.630875f,145.313126f,144.995468f,144.677948f,144.360565f,144.043335f,143.726288f,143.409424f,143.092758f,142.776321f,142.460129f,142.14418f,141.828522f,141.513153f,141.19809f,140.883362f,140.569f,140.255005f,139.941391f,139.62822f,139.315475f,139.003189f,138.691391f,138.380096f,138.069336f,137.75914f,137.449524f,137.140503f,136.832123f,136.524384f,136.217346f,135.911011f,135.605408f,135.300583f,134.996536f,134.693329f,134.390945f,134.089462f,133.788864f,133.489212f,133.190521f,132.892822f,132.596146f,132.300522f,132.005981f,131.71254f,131.420258f,131.129135f,130.839203f,130.550507f,130.263062f,129.976898f,129.692062f,129.408569f,129.126434f,128.845703f,128.566391f,128.288528f,128.012161f,127.737282f,127.463936f,127.192139f,126.921921f,126.653305f,126.386307f,126.120956f,125.857269f,125.595276f,125.334976f,125.076408f,124.819572f,124.564499f,124.311203f,124.059692f,123.80999f,123.562096f,123.316032f,123.071815f,122.829437f,122.588921f,122.350273f,122.113503f,121.878609f,121.645607f,121.41449f,121.185272f,120.957947f,120.732521f,120.509003f,120.287376f,120.067642f,119.849815f,119.633881f,119.41983f,119.207672f,118.997398f,118.788994f,118.582458f,118.377792f,118.174973f,117.974007f,117.774872f,117.577568f,117.382088f,117.188408f,116.996521f,116.806427f,116.618103f,116.431541f,116.246719f,116.063644f,115.882278f,115.702629f,115.524666f,115.348389f,115.173775f,115.000801f,114.829475f,114.65976f,114.491646f,114.325127f,114.160179f,113.996788f,113.834938f,113.674614f,113.5158f,113.358482f,113.202637f,113.048248f,112.895317f,112.743805f,112.593712f,112.445007f,112.297691f,112.151741f,112.007141f,111.863869f,111.721916f,111.581268f,111.44191f,111.303818f,111.166985f,111.031387f,110.897018f,110.763863f,110.631905f,110.501122f,110.371506f,110.24305f,110.115723f,109.989525f,109.864433f,109.74044f,109.617531f,109.495689f,109.374901f,109.25515f,109.136436f,109.01873f,108.902031f,108.786324f,108.671593f,108.557831f,108.445015f,108.333145f,108.222206f,108.112183f,108.003059f,107.894836f,107.787498f,107.68103f,107.575424f,107.470673f,107.366753f,107.263672f,107.1614f,107.059944f,106.959282f,106.859413f,106.760315f,106.661995f,106.56443f,106.467621f,106.371544f,106.276207f,106.181587f,106.087685f,105.994492f,105.901985f,105.810173f,105.719048f,105.628586f,105.538788f,105.449646f,105.361153f,105.2733f,105.186073f,105.09948f,105.013504f,104.928131f,104.843369f,104.759193f,104.675613f,104.592613f,104.510193f,104.428337f,104.347038f,104.266304f,104.186111f,104.106461f,104.027351f,103.948769f,103.870712f,103.793175f,103.716148f,103.639633f,103.563614f,103.488091f,103.413055f,103.338509f,103.264442f,103.190842f,103.117714f,103.045052f,102.972847f,102.901093f,102.829788f,102.758926f,102.688507f,102.618515f,102.548958f,102.47982f,102.41111f,102.342812f,102.274925f,102.207443f,102.140366f,102.073692f,102.007408f,101.941513f,101.875999f,101.810875f,101.746132f,101.681755f,101.61776f,101.554123f,101.490852f,101.42794f,101.365379f,101.303177f,101.241325f,101.17981f,101.118645f,101.057816f,100.997322f,100.937157f,100.877327f,100.817818f,100.758629f,100.69976f,100.641212f,100.582977f,100.525047f,100.46743f,100.41011f,100.353096f,100.296379f,100.23996f,100.18383f,100.127991f,100.072441f,100.017174f,99.9621964f,99.9074936f,99.8530655f,99.798912f,99.7450333f,99.6914215f,99.6380768f,99.5849991f,99.5321808f,99.4796219f,99.4273224f,99.3752747f,99.3234863f,99.2719498f,99.2206573f,99.1696091f,99.1188049f,99.0682449f,99.0179291f,98.9678497f,98.9180069f,98.8683929f,98.8190155f,98.7698593f,98.7209396f,98.6722488f,98.6237717f,98.5755234f,98.5274963f,98.4796829f,98.4320908f,98.3847122f,98.3375473f,98.290596f,98.2438507f,98.1973114f,98.1509781f,98.1048508f,98.0589294f,98.0132065f,97.9676819f,97.9223633f,97.8772278f,97.8322983f,97.7875595f,97.7430115f,97.6986542f,97.65448f,97.6104965f,97.5667038f,97.5230865f,97.47966f,97.436409f,97.3933411f,97.3504562f,97.3077469f,97.2652054f,97.222847f,97.1806564f,97.1386414f,97.0967941f,97.0551224f,97.0136108f,96.9722748f,96.9310989f,96.8900833f,96.8492355f,96.808548f,96.7680283f,96.7276611f,96.6874542f,96.6473999f,96.6075058f,96.5677719f,96.528183f,96.4887543f,96.4494705f,96.4103394f,96.3713608f,96.3325272f,96.2938385f,96.2553024f,96.2169113f,96.1786575f,96.1405563f,96.1025925f,96.0647659f,96.0270844f,95.9895401f,95.9521408f,95.9148712f,95.877739f,95.840744f,95.8038864f,95.7671585f,95.7305603f,95.6940994f,95.6577682f,95.6215668f,95.585495f,95.5495529f,95.5137329f,95.4780426f,95.442482f,95.4070435f,95.371727f,95.3365326f,95.3014603f,95.2665176f,95.2316895f,95.1969833f,95.1623993f,95.1279297f,95.0935745f,95.0593414f,95.0252228f,94.9912262f,94.9573364f,94.9235611f,94.8899002f,94.8563538f,94.8229218f,94.7895966f,94.7563858f,94.7232819f,94.6902847f,94.657402f,94.6246185f,94.5919495f,94.5593872f,94.5269241f,94.4945755f,94.462326f,94.4301758f,94.39814f,94.3661957f,94.3343582f,94.3026276f,94.2709885f,94.2394562f,94.2080154f,94.1766815f,94.1454468f,94.1143036f,94.0832596f,94.0523148f,94.0214615f,93.9907074f,93.9600525f,93.9294815f,93.8990097f,93.8686371f,93.8383484f,93.8081589f,93.7780533f,93.7480469f,93.7181244f,93.6882935f,93.6585541f,93.6289062f,93.5993423f,93.5698624f,93.5404816f,93.5111771f,93.4819641f,93.4528427f,93.4237976f,93.3948441f,93.3659668f,93.3371811f,93.3084793f,93.2798615f,93.2513199f,93.2228622f,93.1944885f,93.1661987f,93.1379929f,93.1098557f,93.08181f,93.0538406f,93.0259476f,92.9981384f,92.9703979f,92.9427414f,92.9151688f,92.8876648f,92.8602448f,92.8328934f,92.8056183f,92.7784271f,92.7513046f,92.7242584f,92.6972809f,92.6703873f,92.6435623f,92.616806f,92.590126f,92.5635223f,92.5369873f,92.5105286f,92.4841309f,92.4578171f,92.4315643f,92.4053879f,92.3792725f,92.3532333f,92.3272629f,92.3013611f,92.275528f,92.2497635f,92.2240601f,92.1984329f,92.1728668f,92.1473694f,92.1219406f,92.0965729f,92.0712738f,92.0460434f,92.020874f,91.9957733f,91.9707336f,91.9457626f,91.920845f,91.8960037f,91.8712158f,91.8464966f,91.8218384f,91.7972412f,91.7727051f,91.74823f,91.7238235f,91.6994705f,91.6751862f,91.6509552f,91.6267853f,91.6026764f,91.5786285f,91.5546341f,91.5307083f,91.5068359f,91.4830246f,91.4592667f,91.4355698f,91.4119263f,91.3883514f,91.3648224f,91.3413544f,91.3179474f,91.2945862f,91.2712936f,91.2480469f,91.2248611f,91.2017288f,91.1786499f,91.155632f,91.1326599f,91.1097488f,91.0868912f,91.0640869f,91.0413361f,91.0186386f,90.9959946f,90.9733963f,90.9508591f,90.9283752f,90.9059372f,90.8835526f,90.8612213f,90.8389435f,90.8167191f,90.7945404f,90.7724152f,90.7503357f,90.7283096f,90.706337f,90.6844101f,90.6625366f,90.6407089f,90.618927f,90.5971985f,90.5755234f,90.553894f,90.5323105f,90.5107727f,90.4892883f,90.4678497f,90.4464645f,90.4251175f,90.4038239f,90.382576f,90.3613739f,90.3402176f,90.3191071f,90.2980423f,90.2770233f,90.2560577f,90.2351303f,90.2142487f,90.1934128f,90.1726227f,90.1518784f,90.1311798f,90.1105194f,90.0899124f,90.0693436f,90.0488205f,90.0283432f,90.0079041f,89.9875107f,89.9671631f,89.9468536f,89.92659f,89.9063721f,89.8861923f,89.8660583f,89.8459625f,89.8259125f,89.8059006f,89.7859344f,89.7660065f,89.7461166f,89.7262726f,89.7064667f,89.6867065f,89.6669846f,89.6473007f,89.6276627f,89.6080627f,89.588501f,89.5689774f,89.5494995f,89.5300598f,89.5106583f,89.4912949f,89.4719696f,89.4526825f,89.4334335f,89.4142303f,89.3950577f,89.3759308f,89.3568344f,89.3377838f,89.3187637f,89.2997894f,89.2808456f,89.26194f,89.2430725f,89.2242432f,89.205452f,89.1866989f,89.1679764f,89.1492996f,89.1306534f,89.1120453f,89.0934677f,89.0749359f,89.0564346f,89.0379639f,89.0195389f,89.0011444f,88.9827881f,88.9644623f,88.9461746f,88.9279251f,88.9097061f,88.8915176f,88.8733673f,88.8552551f,88.8371735f,88.8191299f,88.8011169f,88.7831421f,88.7651978f,88.7472916f,88.7294083f,88.7115707f,88.6937561f,88.6759796f,88.6582413f,88.6405258f,88.6228485f,88.6052094f,88.5875931f,88.570015f,88.5524673f,88.5349579f,88.5174713f,88.5000229f,88.482605f,88.4652176f,88.4478607f,88.430542f,88.4132462f,88.3959885f,88.3787613f,88.3615646f,88.3443985f,88.3272629f,88.3101578f,88.2930832f,88.2760391f,88.2590256f,88.2420425f,88.22509f,88.208168f,88.1912766f,88.1744156f,88.1575851f,88.1407776f,88.1240082f,88.1072693f,88.0905533f,88.0738678f,88.0572128f,88.0405884f,88.0239944f,88.0074234f,87.9908829f,87.9743805f,87.9578934f,87.9414444f,87.9250183f,87.9086227f,87.8922577f,87.8759155f,87.8596115f,87.8433228f,87.8270721f,87.8108444f,87.7946472f,87.7784729f,87.7623291f,87.7462158f,87.7301254f,87.7140656f,87.6980286f,87.6820221f,87.6660385f,87.6500854f,87.6341629f,87.6182632f,87.6023865f,87.5865402f,87.5707245f,87.5549316f,87.5391617f,87.5234222f,87.5077057f,87.4920197f,87.4763565f,87.4607162f,87.4451065f,87.4295197f,87.4139633f,87.3984299f,87.3829193f,87.3674393f,87.3519821f,87.3365479f,87.3211365f,87.3057556f,87.2903976f,87.2750702f,87.259758f,87.2444763f,87.2292175f,87.2139893f,87.1987762f,87.1835938f,87.1684341f,87.1532974f,87.1381912f,87.1231003f,87.1080399f,87.0930023f,87.0779877f,87.0629959f,87.048027f,87.0330811f,87.0181656f,87.0032654f,86.9883957f,86.9735413f,86.9587173f,86.9439163f,86.9291382f,86.9143753f,86.8996429f,86.8849335f,86.8702469f,86.8555832f,86.8409424f,86.8263245f,86.8117218f,86.7971497f,86.7826004f,86.768074f,86.7535629f,86.7390823f,86.724617f,86.7101822f,86.6957626f,86.681366f,86.6669922f,86.6526413f,86.6383133f,86.6240005f,86.6097183f,86.5954514f,86.5812073f,86.5669861f,86.5527878f,86.5386124f,86.5244522f,86.5103149f,86.4962006f,86.4821091f,86.4680328f,86.4539871f,86.4399567f,86.4259415f,86.4119568f,86.3979874f,86.3840408f,86.3701172f,86.3562088f,86.3423233f,86.3284607f,86.3146133f,86.3007889f,86.2869873f,86.273201f,86.2594376f,86.245697f,86.2319717f,86.2182693f,86.2045898f,86.1909256f,86.1772842f,86.1636581f,86.1500549f,86.1364746f,86.1229095f,86.1093597f,86.0958405f,86.0823288f,86.0688477f,86.0553818f,86.0419312f,86.0285034f,86.0150909f,86.0017014f,85.9883347f,85.9749832f,85.961647f,85.9483337f,85.9350433f,85.9217606f,85.9085083f,85.8952637f,85.8820496f,85.8688431f,85.8556595f,85.8424988f,85.8293533f,85.8162231f,85.8031158f,85.7900238f,85.7769547f,85.7639008f,85.7508621f,85.7378464f,85.7248459f,85.7118607f,85.6988983f,85.6859512f,85.673027f,85.6601181f,85.6472244f,85.634346f,85.6214905f,85.6086502f,85.5958328f,85.5830231f,85.5702362f,85.5574722f,85.5447159f,85.5319824f,85.5192642f,85.5065689f,85.4938812f,85.4812164f,85.4685669f,85.4559402f,85.4433212f,85.4307251f,85.4181442f,85.4055786f,85.3930359f,85.3805084f,85.3679886f,85.3554993f,85.3430176f,85.3305511f,85.3181076f,85.3056793f,85.2932663f,85.2808685f,85.268486f,85.2561188f,85.2437744f,85.2314453f,85.2191315f,85.2068329f,85.1945496f,85.1822815f,85.1700287f,85.1577911f,85.1455765f,85.1333771f,85.1211853f,85.1090164f,85.0968628f,85.0847244f,85.0726013f,85.0604935f,85.0484009f,85.0363235f,85.0242615f,85.0122147f,85.0001907f,84.9881744f,84.9761734f,84.9641953f,84.9522247f,84.9402695f,84.9283371f,84.9164124f,84.9045105f,84.8926163f,84.8807373f,84.8688812f,84.8570328f,84.8451996f,84.8333817f,84.8215866f,84.8097992f,84.798027f,84.7862701f,84.7745285f,84.7628021f,84.751091f,84.7393875f,84.7277069f,84.7160416f,84.7043839f,84.692749f,84.6811218f,84.6695099f,84.6579132f,84.6463394f,84.6347656f,84.6232147f,84.6116791f,84.6001511f,84.5886459f,84.5771484f,84.5656662f,84.5541992f,84.5427475f,84.531311f,84.5198822f,84.5084763f,84.4970779f,84.4856949f,84.4743271f,84.4629669f,84.4516296f,84.4403f,84.4289856f,84.4176865f,84.4064026f,84.3951263f,84.3838654f,84.3726196f,84.3613892f,84.350174f,84.3389664f,84.327774f,84.316597f,84.3054352f,84.294281f,84.2831421f,84.2720184f,84.26091f,84.2498093f,84.2387238f,84.2276535f,84.2165985f,84.2055511f,84.194519f,84.1835022f,84.172493f,84.161499f,84.1505203f,84.1395569f,84.1286011f,84.1176605f,84.1067352f,84.0958176f,84.0849152f,84.074028f,84.0631485f,84.0522842f,84.0414276f,84.0305939f,84.0197678f,84.0089493f,83.9981537f,83.9873657f,83.9765854f,83.9658203f,83.9550705f,83.9443359f,83.933609f,83.9228897f,83.9121933f,83.9015045f,83.8908234f,83.8801575f,83.8695068f,83.8588638f,83.8482361f,83.8376236f,83.8270187f,83.8164291f,83.8058472f,83.7952805f,83.7847214f,83.7741776f,83.763649f,83.7531281f,83.7426224f,83.7321243f,83.7216415f,83.711174f,83.7007065f,83.6902618f,83.6798248f,83.6694031f,83.658989f,83.6485825f,83.6381912f,83.6278152f,83.6174469f,83.6070938f,83.5967484f,83.5864182f,83.5760956f,83.5657883f,83.5554886f,83.5452042f,83.5349274f,83.5246658f,83.5144119f,83.5041733f,83.4939423f,83.4837265f,83.4735184f,83.4633255f,83.4531403f,83.4429626f,83.4328003f,83.4226532f,83.4125137f,83.4023819f,83.3922653f,83.3821564f,83.3720627f,83.3619766f,83.3519058f,83.3418427f,83.3317871f,83.3217468f,83.3117142f,83.3016968f,83.291687f,83.2816925f,83.2717056f,83.2617264f,83.2517624f,83.241806f,83.2318649f,83.2219315f,83.2120056f,83.202095f,83.1921921f,83.1823044f,83.1724243f,83.1625519f,83.1526947f,83.1428452f,83.1330109f,83.1231842f,83.1133652f,83.1035538f,83.0937576f,83.0839767f,83.0741959f,83.0644302f,83.0546799f,83.0449295f,83.0351944f,83.0254745f,83.0157547f,83.0060577f,82.9963608f,82.9866791f,82.977005f,82.9673386f,82.9576874f,82.9480438f,82.9384079f,82.9287872f,82.9191742f,82.9095688f,82.8999786f,82.8903961f,82.8808212f,82.871254f,82.861702f,82.8521576f,82.8426208f,82.8330994f,82.8235855f,82.8140793f,82.8045883f,82.795105f,82.7856293f,82.7761612f,82.7667007f,82.7572556f,82.747818f,82.7383957f,82.728981f,82.7195663f,82.7101746f,82.7007828f,82.6914062f,82.6820374f,82.6726761f,82.6633224f,82.6539841f,82.6446533f,82.6353302f,82.6260147f,82.6167145f,82.6074142f,82.5981293f,82.5888596f,82.5795898f,82.5703354f,82.5610886f,82.5518494f,82.5426178f,82.5334015f,82.5241852f,82.5149841f,82.5057983f,82.4966125f,82.4874344f,82.4782715f,82.4691162f,82.4599686f,82.4508286f,82.4417038f,82.4325867f,82.4234695f,82.4143677f,82.4052811f,82.3961945f,82.3871231f,82.3780518f,82.3689957f,82.3599472f,82.3509064f,82.3418808f,82.3328552f,82.3238449f,82.3148422f,82.3058472f,82.2968597f,82.2878799f};
class mydspSIG1 {
	
  private:
	
	int fmydspSIG1Wave0_idx;
	
  public:
	
	int getNumInputsmydspSIG1() {
		return 0;
	}
	int getNumOutputsmydspSIG1() {
		return 1;
	}
	
	void instanceInitmydspSIG1(int sample_rate) {
		fmydspSIG1Wave0_idx = 0;
	}
	
	void fillmydspSIG1(int count, float* table) {
		for (int i2 = 0; i2 < count; i2 = i2 + 1) {
			table[i2] = fmydspSIG1Wave0[fmydspSIG1Wave0_idx];
			fmydspSIG1Wave0_idx = (1 + fmydspSIG1Wave0_idx) % 2001;
		}
	}

};

static mydspSIG1* newmydspSIG1() { return (mydspSIG1*)new mydspSIG1(); }
static void deletemydspSIG1(mydspSIG1* dsp) { delete dsp; }

static const float fmydspSIG2Wave0[100] = {0.0f,-0.0296990145f,-0.0599780679f,-0.0908231661f,-0.122163236f,-0.15376009f,-0.184938014f,-0.214177266f,-0.239335433f,-0.259232581f,-0.274433911f,-0.286183298f,-0.295538545f,-0.303222328f,-0.309706241f,-0.315301329f,-0.320218444f,-0.324604988f,-0.328567117f,-0.332183361f,-0.335513115f,-0.338602364f,-0.341487259f,-0.344196707f,-0.346754223f,-0.349179149f,-0.351487488f,-0.35369277f,-0.35580641f,-0.357838273f,-0.359796762f,-0.36168924f,-0.363522142f,-0.365301102f,-0.367031157f,-0.368716747f,-0.370361924f,-0.371970236f,-0.373544991f,-0.375089139f,-0.376605392f,-0.378096253f,-0.379564017f,-0.38101083f,-0.38243863f,-0.383849323f,-0.385244638f,-0.386626214f,-0.38799566f,-0.389354438f,-0.390703976f,-0.392045677f,-0.393380851f,-0.394710839f,-0.396036893f,-0.397360265f,-0.398682207f,-0.40000397f,-0.401326776f,-0.402651846f,-0.403980494f,-0.405313969f,-0.406653613f,-0.408000737f,-0.409356743f,-0.41072312f,-0.412101358f,-0.413493067f,-0.414899886f,-0.416323662f,-0.417766303f,-0.419229805f,-0.420716405f,-0.422228485f,-0.42376864f,-0.425339758f,-0.426944911f,-0.428587586f,-0.430271626f,-0.432001382f,-0.433781654f,-0.435617924f,-0.437516481f,-0.439484537f,-0.441530377f,-0.443663776f,-0.445896149f,-0.448241174f,-0.450715303f,-0.453338623f,-0.456136048f,-0.45913893f,-0.462387681f,-0.465935349f,-0.469853997f,-0.474244624f,-0.479255259f,-0.485115886f,-0.492212713f,-0.501272738f};
class mydspSIG2 {
	
  private:
	
	int fmydspSIG2Wave0_idx;
	
  public:
	
	int getNumInputsmydspSIG2() {
		return 0;
	}
	int getNumOutputsmydspSIG2() {
		return 1;
	}
	
	void instanceInitmydspSIG2(int sample_rate) {
		fmydspSIG2Wave0_idx = 0;
	}
	
	void fillmydspSIG2(int count, float* table) {
		for (int i3 = 0; i3 < count; i3 = i3 + 1) {
			table[i3] = fmydspSIG2Wave0[fmydspSIG2Wave0_idx];
			fmydspSIG2Wave0_idx = (1 + fmydspSIG2Wave0_idx) % 100;
		}
	}

};

static mydspSIG2* newmydspSIG2() { return (mydspSIG2*)new mydspSIG2(); }
static void deletemydspSIG2(mydspSIG2* dsp) { delete dsp; }

static float mydsp_faustpower2_f(float value) {
	return value * value;
}
static float ftbl0mydspSIG0[2001];
static float ftbl1mydspSIG1[2001];
static float ftbl2mydspSIG2[100];

class mydsp : public dsp {
	
 private:
	
	FAUSTFLOAT fVslider0;
	int fSampleRate;
	float fConst1;
	FAUSTFLOAT fVslider1;
	FAUSTFLOAT fVslider2;
	float fConst2;
	FAUSTFLOAT fVslider3;
	float fConst6;
	float fConst8;
	float fConst9;
	float fRec3[2];
	float fConst11;
	float fConst12;
	float fConst13;
	FAUSTFLOAT fVslider4;
	float fConst14;
	float fRec5[2];
	float fConst16;
	float fConst17;
	float fRec8[2];
	float fConst19;
	float fConst20;
	float fRec12[2];
	float fConst21;
	FAUSTFLOAT fVslider5;
	float fVec0[2];
	float fConst23;
	float fConst24;
	float fConst25;
	FAUSTFLOAT fVslider6;
	float fRec14[2];
	float fVec1[2];
	float fRec13[2];
	FAUSTFLOAT fVslider7;
	float fRec15[2];
	float fRec11[3];
	float fConst26;
	float fConst27;
	float fRec10[2];
	float fVec2[2];
	float fRec9[2];
	float fRec7[3];
	float fRec6[2];
	float fVec3[2];
	float fRec4[2];
	float fRec2[3];
	float fRec1[2];
	FAUSTFLOAT fVslider8;
	float fRec16[2];
	float fConst28;
	float fRec0[4];
	int IOTA0;
	float fVec4[256];
	
 public:
	
	void metadata(Meta* m) { 
		m->declare("author", "Guitarix project (http://guitarix.sourceforge.net/)");
		m->declare("basics_lib_name", "Faust Basic Element Library");
		m->declare("basics_lib_version", "0.6");
		m->declare("compilation_options", "-single -scal -I libraries/ -I project/ -lang wasm");
		m->declare("compile_options", "-lang cpp -es 1 -mcd 16 -single -ftz 0");
		m->declare("copyright", "Guitarix project");
		m->declare("filename", "guitarix.dsp");
		m->declare("filters_lib_conv_author", "Julius O. Smith III");
		m->declare("filters_lib_conv_copyright", "Copyright (C) 2003-2019 by Julius O. Smith III <<EMAIL>>");
		m->declare("filters_lib_conv_license", "MIT-style STK-4.3 license");
		m->declare("filters_lib_fir_author", "Julius O. Smith III");
		m->declare("filters_lib_fir_copyright", "Copyright (C) 2003-2019 by Julius O. Smith III <<EMAIL>>");
		m->declare("filters_lib_fir_license", "MIT-style STK-4.3 license");
		m->declare("filters_lib_highpass_author", "Julius O. Smith III");
		m->declare("filters_lib_highpass_copyright", "Copyright (C) 2003-2019 by Julius O. Smith III <<EMAIL>>");
		m->declare("filters_lib_iir_author", "Julius O. Smith III");
		m->declare("filters_lib_iir_copyright", "Copyright (C) 2003-2019 by Julius O. Smith III <<EMAIL>>");
		m->declare("filters_lib_iir_license", "MIT-style STK-4.3 license");
		m->declare("filters_lib_lowpass0_highpass1", "Copyright (C) 2003-2019 by Julius O. Smith III <<EMAIL>>");
		m->declare("filters_lib_lowpass0_highpass1_author", "Julius O. Smith III");
		m->declare("filters_lib_lowpass_author", "Julius O. Smith III");
		m->declare("filters_lib_lowpass_copyright", "Copyright (C) 2003-2019 by Julius O. Smith III <<EMAIL>>");
		m->declare("filters_lib_lowpass_license", "MIT-style STK-4.3 license");
		m->declare("filters_lib_name", "Faust Filters Library");
		m->declare("filters_lib_tf1_author", "Julius O. Smith III");
		m->declare("filters_lib_tf1_copyright", "Copyright (C) 2003-2019 by Julius O. Smith III <<EMAIL>>");
		m->declare("filters_lib_tf1_license", "MIT-style STK-4.3 license");
		m->declare("filters_lib_tf1s_author", "Julius O. Smith III");
		m->declare("filters_lib_tf1s_copyright", "Copyright (C) 2003-2019 by Julius O. Smith III <<EMAIL>>");
		m->declare("filters_lib_tf1s_license", "MIT-style STK-4.3 license");
		m->declare("filters_lib_version", "0.3");
		m->declare("library_path0", "/libraries/tonestacks.lib");
		m->declare("library_path1", "/libraries/tubes.lib");
		m->declare("library_path2", "/libraries/filters.lib");
		m->declare("library_path3", "/libraries/maths.lib");
		m->declare("library_path4", "/libraries/platform.lib");
		m->declare("library_path5", "/libraries/basics.lib");
		m->declare("library_path6", "/libraries/signals.lib");
		m->declare("license", "LGPL");
		m->declare("maths_lib_author", "GRAME");
		m->declare("maths_lib_copyright", "GRAME");
		m->declare("maths_lib_license", "LGPL with exception");
		m->declare("maths_lib_name", "Faust Math Library");
		m->declare("maths_lib_version", "2.5");
		m->declare("name", "guitarix");
		m->declare("platform_lib_name", "Generic Platform Library");
		m->declare("platform_lib_version", "0.2");
		m->declare("signals_lib_name", "Faust Signal Routing Library");
		m->declare("signals_lib_version", "0.1");
		m->declare("tonestacks_lib_author", "Guitarix project (<http://guitarix.sourceforge.net/>)");
		m->declare("tonestacks_lib_copyright", "Guitarix project");
		m->declare("tonestacks_lib_license", "LGPL");
		m->declare("tonestacks_lib_name", "Faust Tonestack Emulation Library");
		m->declare("tonestacks_lib_version", "0.28");
		m->declare("version", "0.29");
	}

	virtual int getNumInputs() {
		return 1;
	}
	virtual int getNumOutputs() {
		return 1;
	}
	
	static void classInit(int sample_rate) {
		mydspSIG0* sig0 = newmydspSIG0();
		sig0->instanceInitmydspSIG0(sample_rate);
		sig0->fillmydspSIG0(2001, ftbl0mydspSIG0);
		mydspSIG1* sig1 = newmydspSIG1();
		sig1->instanceInitmydspSIG1(sample_rate);
		sig1->fillmydspSIG1(2001, ftbl1mydspSIG1);
		mydspSIG2* sig2 = newmydspSIG2();
		sig2->instanceInitmydspSIG2(sample_rate);
		sig2->fillmydspSIG2(100, ftbl2mydspSIG2);
		deletemydspSIG0(sig0);
		deletemydspSIG1(sig1);
		deletemydspSIG2(sig2);
	}
	
	virtual void instanceConstants(int sample_rate) {
		fSampleRate = sample_rate;
		float fConst0 = std::min<float>(192000.0f, std::max<float>(1.0f, float(fSampleRate)));
		fConst1 = 2.0f * fConst0;
		fConst2 = mydsp_faustpower2_f(fConst1);
		float fConst3 = std::tan(97.3893738f / fConst0);
		float fConst4 = 1.0f / fConst3;
		float fConst5 = fConst4 + 1.0f;
		fConst6 = 1.0f / (fConst3 * fConst5);
		float fConst7 = 1.0f / std::tan(609.468994f / fConst0);
		fConst8 = 1.0f / (fConst7 + 1.0f);
		fConst9 = 1.0f - fConst7;
		float fConst10 = 1.0f / std::tan(20517.7422f / fConst0);
		fConst11 = 1.0f / (fConst10 + 1.0f);
		fConst12 = 1.0f - fConst10;
		fConst13 = 44.0999985f / fConst0;
		fConst14 = 1.0f - fConst13;
		float fConst15 = 1.0f / std::tan(414.690216f / fConst0);
		fConst16 = 1.0f / (fConst15 + 1.0f);
		fConst17 = 1.0f - fConst15;
		float fConst18 = 1.0f / std::tan(270.176971f / fConst0);
		fConst19 = 1.0f / (fConst18 + 1.0f);
		fConst20 = 1.0f - fConst18;
		fConst21 = 3.14159274f / fConst0;
		float fConst22 = 0.000441799988f * fConst0;
		fConst23 = 1.0f / (fConst22 + 1.0f);
		fConst24 = 1.0f - fConst22;
		fConst25 = 9.40000007e-08f * fConst0;
		fConst26 = 0.0f - fConst6;
		fConst27 = (1.0f - fConst4) / fConst5;
		fConst28 = 3.0f * fConst1;
	}
	
	virtual void instanceResetUserInterface() {
		fVslider0 = FAUSTFLOAT(100.0f);
		fVslider1 = FAUSTFLOAT(0.5f);
		fVslider2 = FAUSTFLOAT(0.5f);
		fVslider3 = FAUSTFLOAT(0.5f);
		fVslider4 = FAUSTFLOAT(-6.0f);
		fVslider5 = FAUSTFLOAT(400.0f);
		fVslider6 = FAUSTFLOAT(0.5f);
		fVslider7 = FAUSTFLOAT(-16.0f);
		fVslider8 = FAUSTFLOAT(-6.0f);
	}
	
	virtual void instanceClear() {
		for (int l0 = 0; l0 < 2; l0 = l0 + 1) {
			fRec3[l0] = 0.0f;
		}
		for (int l1 = 0; l1 < 2; l1 = l1 + 1) {
			fRec5[l1] = 0.0f;
		}
		for (int l2 = 0; l2 < 2; l2 = l2 + 1) {
			fRec8[l2] = 0.0f;
		}
		for (int l3 = 0; l3 < 2; l3 = l3 + 1) {
			fRec12[l3] = 0.0f;
		}
		for (int l4 = 0; l4 < 2; l4 = l4 + 1) {
			fVec0[l4] = 0.0f;
		}
		for (int l5 = 0; l5 < 2; l5 = l5 + 1) {
			fRec14[l5] = 0.0f;
		}
		for (int l6 = 0; l6 < 2; l6 = l6 + 1) {
			fVec1[l6] = 0.0f;
		}
		for (int l7 = 0; l7 < 2; l7 = l7 + 1) {
			fRec13[l7] = 0.0f;
		}
		for (int l8 = 0; l8 < 2; l8 = l8 + 1) {
			fRec15[l8] = 0.0f;
		}
		for (int l9 = 0; l9 < 3; l9 = l9 + 1) {
			fRec11[l9] = 0.0f;
		}
		for (int l10 = 0; l10 < 2; l10 = l10 + 1) {
			fRec10[l10] = 0.0f;
		}
		for (int l11 = 0; l11 < 2; l11 = l11 + 1) {
			fVec2[l11] = 0.0f;
		}
		for (int l12 = 0; l12 < 2; l12 = l12 + 1) {
			fRec9[l12] = 0.0f;
		}
		for (int l13 = 0; l13 < 3; l13 = l13 + 1) {
			fRec7[l13] = 0.0f;
		}
		for (int l14 = 0; l14 < 2; l14 = l14 + 1) {
			fRec6[l14] = 0.0f;
		}
		for (int l15 = 0; l15 < 2; l15 = l15 + 1) {
			fVec3[l15] = 0.0f;
		}
		for (int l16 = 0; l16 < 2; l16 = l16 + 1) {
			fRec4[l16] = 0.0f;
		}
		for (int l17 = 0; l17 < 3; l17 = l17 + 1) {
			fRec2[l17] = 0.0f;
		}
		for (int l18 = 0; l18 < 2; l18 = l18 + 1) {
			fRec1[l18] = 0.0f;
		}
		for (int l19 = 0; l19 < 2; l19 = l19 + 1) {
			fRec16[l19] = 0.0f;
		}
		for (int l20 = 0; l20 < 4; l20 = l20 + 1) {
			fRec0[l20] = 0.0f;
		}
		IOTA0 = 0;
		for (int l21 = 0; l21 < 256; l21 = l21 + 1) {
			fVec4[l21] = 0.0f;
		}
	}
	
	virtual void init(int sample_rate) {
		classInit(sample_rate);
		instanceInit(sample_rate);
	}
	virtual void instanceInit(int sample_rate) {
		instanceConstants(sample_rate);
		instanceResetUserInterface();
		instanceClear();
	}
	
	virtual mydsp* clone() {
		return new mydsp();
	}
	
	virtual int getSampleRate() {
		return fSampleRate;
	}
	
	virtual void buildUserInterface(UI* ui_interface) {
		ui_interface->openHorizontalBox("Guitarix");
		ui_interface->declare(0, "0", "");
		ui_interface->openHorizontalBox("TubeScreamer");
		ui_interface->declare(&fVslider6, "name", "Drive");
		ui_interface->declare(&fVslider6, "style", "knob");
		ui_interface->addVerticalSlider("drive", &fVslider6, FAUSTFLOAT(0.5f), FAUSTFLOAT(0.0f), FAUSTFLOAT(1.0f), FAUSTFLOAT(0.00999999978f));
		ui_interface->declare(&fVslider7, "name", "Level");
		ui_interface->declare(&fVslider7, "style", "knob");
		ui_interface->addVerticalSlider("level", &fVslider7, FAUSTFLOAT(-16.0f), FAUSTFLOAT(-20.0f), FAUSTFLOAT(4.0f), FAUSTFLOAT(0.100000001f));
		ui_interface->declare(&fVslider5, "log", "");
		ui_interface->declare(&fVslider5, "name", "Tone");
		ui_interface->declare(&fVslider5, "style", "knob");
		ui_interface->addVerticalSlider("tone", &fVslider5, FAUSTFLOAT(400.0f), FAUSTFLOAT(100.0f), FAUSTFLOAT(1000.0f), FAUSTFLOAT(1.02999997f));
		ui_interface->closeBox();
		ui_interface->declare(0, "1", "");
		ui_interface->openHorizontalBox("preamp: 12AX7");
		ui_interface->declare(&fVslider4, "0", "");
		ui_interface->declare(&fVslider4, "style", "knob");
		ui_interface->addVerticalSlider("Pregain", &fVslider4, FAUSTFLOAT(-6.0f), FAUSTFLOAT(-20.0f), FAUSTFLOAT(20.0f), FAUSTFLOAT(0.100000001f));
		ui_interface->declare(&fVslider8, "1", "");
		ui_interface->declare(&fVslider8, "style", "knob");
		ui_interface->addVerticalSlider("Gain", &fVslider8, FAUSTFLOAT(-6.0f), FAUSTFLOAT(-20.0f), FAUSTFLOAT(20.0f), FAUSTFLOAT(0.100000001f));
		ui_interface->closeBox();
		ui_interface->declare(0, "2", "");
		ui_interface->openHorizontalBox("tonestack: jcm2000");
		ui_interface->declare(&fVslider3, "2", "");
		ui_interface->declare(&fVslider3, "style", "knob");
		ui_interface->addVerticalSlider("Treble", &fVslider3, FAUSTFLOAT(0.5f), FAUSTFLOAT(0.0f), FAUSTFLOAT(1.0f), FAUSTFLOAT(0.00999999978f));
		ui_interface->declare(&fVslider1, "3", "");
		ui_interface->declare(&fVslider1, "style", "knob");
		ui_interface->addVerticalSlider("Middle", &fVslider1, FAUSTFLOAT(0.5f), FAUSTFLOAT(0.0f), FAUSTFLOAT(1.0f), FAUSTFLOAT(0.00999999978f));
		ui_interface->declare(&fVslider2, "4", "");
		ui_interface->declare(&fVslider2, "style", "knob");
		ui_interface->addVerticalSlider("Bass", &fVslider2, FAUSTFLOAT(0.5f), FAUSTFLOAT(0.0f), FAUSTFLOAT(1.0f), FAUSTFLOAT(0.00999999978f));
		ui_interface->closeBox();
		ui_interface->declare(0, "3", "");
		ui_interface->openHorizontalBox("Cabinet");
		ui_interface->declare(&fVslider0, "5", "");
		ui_interface->declare(&fVslider0, "style", "knob");
		ui_interface->addVerticalSlider("amount", &fVslider0, FAUSTFLOAT(100.0f), FAUSTFLOAT(0.0f), FAUSTFLOAT(100.0f), FAUSTFLOAT(1.0f));
		ui_interface->closeBox();
		ui_interface->closeBox();
	}
	
	virtual void compute(int count, FAUSTFLOAT** RESTRICT inputs, FAUSTFLOAT** RESTRICT outputs) {
		FAUSTFLOAT* input0 = inputs[0];
		FAUSTFLOAT* output0 = outputs[0];
		float fSlow0 = float(fVslider0);
		float fSlow1 = float(fVslider1);
		float fSlow2 = std::exp(3.4000001f * (float(fVslider2) + -1.0f));
		float fSlow3 = 0.000549999997f * fSlow1 + 0.0225000009f * fSlow2;
		float fSlow4 = fConst1 * (fSlow3 + 0.0031514999f);
		float fSlow5 = 3.09374997e-07f * fSlow1;
		float fSlow6 = fSlow1 * ((1.23749996e-05f * fSlow2 + -2.99475005e-07f) - fSlow5) + 3.10860014e-05f * fSlow2 + 1.08514996e-06f;
		float fSlow7 = 1.8513e-09f * fSlow2 - 4.62824987e-11f * fSlow1;
		float fSlow8 = 3.3880001e-09f * fSlow2;
		float fSlow9 = fSlow1 * (fSlow7 + -3.84174983e-11f) + fSlow8 + 8.4699997e-11f;
		float fSlow10 = fConst1 * fSlow9;
		float fSlow11 = -1.0f - (fSlow4 + fConst2 * (fSlow6 + fSlow10));
		float fSlow12 = (1.0f - 0.00999999978f * fSlow0 + 4.88281012e-06f * fSlow0) / fSlow11;
		float fSlow13 = float(fVslider3);
		float fSlow14 = fConst1 * (fSlow3 + 0.000125000006f * fSlow13 + 0.000562499976f);
		float fSlow15 = 3.08000011e-07f * fSlow13 + fSlow1 * (3.78125009e-07f - fSlow5) + fSlow2 * (1.23749996e-05f * fSlow1 + 3.98200018e-06f) + 9.95500002e-08f;
		float fSlow16 = fSlow1 * (fSlow7 + 4.62824987e-11f) + fSlow13 * (fSlow8 - 8.4699997e-11f * (fSlow1 + -1.0f));
		float fSlow17 = fConst1 * fSlow16;
		float fSlow18 = 0.0f - (fSlow14 + fConst2 * (fSlow15 + fSlow17));
		float fSlow19 = fConst13 * std::pow(10.0f, 0.0500000007f * float(fVslider4));
		float fSlow20 = 1.0f / std::tan(fConst21 * float(fVslider5));
		float fSlow21 = 1.0f / (fSlow20 + 1.0f);
		float fSlow22 = 1.0f - fSlow20;
		float fSlow23 = fConst25 * (500000.0f * float(fVslider6) + 55700.0f);
		float fSlow24 = fSlow23 + 1.0f;
		float fSlow25 = 1.0f - fSlow23;
		float fSlow26 = fConst13 * std::pow(10.0f, 0.0500000007f * float(fVslider7));
		float fSlow27 = fConst13 * std::pow(10.0f, 0.0500000007f * float(fVslider8));
		float fSlow28 = 1.0f / fSlow11;
		float fSlow29 = fConst28 * fSlow9;
		float fSlow30 = fConst2 * (fSlow6 + fSlow29) + -3.0f - fSlow4;
		float fSlow31 = fSlow4 + fConst2 * (fSlow6 - fSlow29) + -3.0f;
		float fSlow32 = fSlow4 + -1.0f - fConst2 * (fSlow6 - fSlow10);
		float fSlow33 = fConst28 * fSlow16;
		float fSlow34 = fConst2 * (fSlow15 + fSlow33) - fSlow14;
		float fSlow35 = fSlow14 + fConst2 * (fSlow15 - fSlow33);
		float fSlow36 = fSlow14 - fConst2 * (fSlow15 - fSlow17);
		float fSlow37 = 0.00999999978f * fSlow0 / fSlow11;
		for (int i0 = 0; i0 < count; i0 = i0 + 1) {
			fRec3[0] = fConst8 * (0.00820000004f * (fRec2[1] + fRec2[2]) - fConst9 * fRec3[1]);
			fRec5[0] = fSlow19 + fConst14 * fRec5[1];
			fRec8[0] = fConst16 * (0.0149999997f * (fRec7[1] + fRec7[2]) - fConst17 * fRec8[1]);
			fRec12[0] = fConst19 * (0.0270000007f * (fRec11[1] + fRec11[2]) - fConst20 * fRec12[1]);
			float fTemp0 = float(input0[i0]);
			fVec0[0] = fTemp0;
			fRec14[0] = 0.0f - fConst23 * (fConst24 * fRec14[1] - (fSlow24 * fTemp0 + fSlow25 * fVec0[1]));
			float fTemp1 = fRec14[0] - fTemp0;
			float fTemp2 = std::fabs(fTemp1);
			float fTemp3 = 101.970001f * fTemp2 / (fTemp2 + 3.0f);
			float fTemp4 = std::max<float>(0.0f, std::min<float>(98.0f, std::floor(fTemp3)));
			float fElse2 = fTemp3 - fTemp4;
			float fThen3 = ((fTemp4 < 98.0f) ? fElse2 : 98.0f);
			float fTemp5 = ((0.0f < fTemp4) ? 0.0f : fThen3);
			float fTemp6 = fTemp0 - float(((fTemp2 * float(((fTemp1 < 0.0f) ? 1 : -1)) < 0.0f) ? -1 : 1)) * std::fabs(ftbl2mydspSIG2[int(fTemp4)] * (1.0f - fTemp5) + fTemp5 * ftbl2mydspSIG2[int(fTemp4 + 1.0f)]);
			fVec1[0] = fTemp6;
			fRec13[0] = 0.0f - fSlow21 * (fSlow22 * fRec13[1] - (fTemp6 + fVec1[1]));
			fRec15[0] = fSlow26 + fConst14 * fRec15[1];
			float fTemp7 = 200.0f * (fRec12[0] + fRec13[0] * fRec15[0] + 3.41834402f);
			float fTemp8 = std::max<float>(0.0f, std::min<float>(1999.0f, std::floor(fTemp7)));
			float fElse4 = fTemp7 - fTemp8;
			float fThen5 = ((fTemp8 < 1999.0f) ? fElse4 : 1999.0f);
			float fTemp9 = ((0.0f < fTemp8) ? 0.0f : fThen5);
			fRec11[0] = ftbl1mydspSIG1[int(fTemp8)] * (1.0f - fTemp9) + fTemp9 * ftbl1mydspSIG1[int(fTemp8 + 1.0f)] + -191.420151f;
			fRec10[0] = 0.0250000004f * (fConst6 * fRec11[0] + fConst26 * fRec11[1]) - fConst27 * fRec10[1];
			float fTemp10 = fRec10[0] * fRec5[0];
			fVec2[0] = fTemp10;
			fRec9[0] = 0.0f - fConst11 * (fConst12 * fRec9[1] - (fTemp10 + fVec2[1]));
			float fTemp11 = 200.0f * (fRec8[0] + fRec9[0] + 3.79571509f);
			float fTemp12 = std::max<float>(0.0f, std::min<float>(1999.0f, std::floor(fTemp11)));
			float fElse6 = fTemp11 - fTemp12;
			float fThen7 = ((fTemp12 < 1999.0f) ? fElse6 : 1999.0f);
			float fTemp13 = ((0.0f < fTemp12) ? 0.0f : fThen7);
			fRec7[0] = ftbl0mydspSIG0[int(fTemp12)] * (1.0f - fTemp13) + fTemp13 * ftbl0mydspSIG0[int(fTemp12 + 1.0f)] + -169.71434f;
			fRec6[0] = 0.0250000004f * (fConst6 * fRec7[0] + fConst26 * fRec7[1]) - fConst27 * fRec6[1];
			float fTemp14 = fRec5[0] * fRec6[0];
			fVec3[0] = fTemp14;
			fRec4[0] = 0.0f - fConst11 * (fConst12 * fRec4[1] - (fTemp14 + fVec3[1]));
			float fTemp15 = 200.0f * (fRec3[0] + fRec4[0] + 4.15929699f);
			float fTemp16 = std::max<float>(0.0f, std::min<float>(1999.0f, std::floor(fTemp15)));
			float fElse8 = fTemp15 - fTemp16;
			float fThen9 = ((fTemp16 < 1999.0f) ? fElse8 : 1999.0f);
			float fTemp17 = ((0.0f < fTemp16) ? 0.0f : fThen9);
			fRec2[0] = ftbl0mydspSIG0[int(fTemp16)] * (1.0f - fTemp17) + fTemp17 * ftbl0mydspSIG0[int(fTemp16 + 1.0f)] + -147.47525f;
			fRec1[0] = 0.0250000004f * (fConst6 * fRec2[0] + fConst26 * fRec2[1]) - fConst27 * fRec1[1];
			fRec16[0] = fSlow27 + fConst14 * fRec16[1];
			fRec0[0] = fRec1[0] * fRec16[0] - fSlow28 * (fSlow30 * fRec0[1] + fSlow31 * fRec0[2] + fSlow32 * fRec0[3]);
			float fTemp18 = fSlow18 * fRec0[0] + fSlow34 * fRec0[1] + fSlow35 * fRec0[2] + fSlow36 * fRec0[3];
			float fTemp19 = fSlow37 * fTemp18;
			fVec4[IOTA0 & 255] = fTemp19;
			output0[i0] = FAUSTFLOAT((fSlow12 * fTemp18 + 0.75f * (fVec4[(IOTA0 - 24) & 255] + fVec4[(IOTA0 - 88) & 255]) + 0.000720214972f * fVec4[(IOTA0 - 63) & 255] + 0.000402831996f * fVec4[(IOTA0 - 62) & 255] + 0.00170897995f * fVec4[(IOTA0 - 61) & 255] + 0.00140380999f * fVec4[(IOTA0 - 60) & 255] + 0.00294188992f * fVec4[(IOTA0 - 59) & 255] + 0.00272216997f * fVec4[(IOTA0 - 58) & 255] + 0.00455321977f * fVec4[(IOTA0 - 57) & 255] + 0.00458983984f * fVec4[(IOTA0 - 56) & 255] + 0.00677490002f * fVec4[(IOTA0 - 55) & 255] + 0.0071899402f * fVec4[(IOTA0 - 54) & 255] + 0.00942382962f * fVec4[(IOTA0 - 53) & 255] + 0.0103882002f * fVec4[(IOTA0 - 52) & 255] + 0.0130005004f * fVec4[(IOTA0 - 51) & 255] + 0.0157226995f * fVec4[(IOTA0 - 50) & 255] + 0.0182006992f * fVec4[(IOTA0 - 49) & 255] + 0.0217285007f * fVec4[(IOTA0 - 48) & 255] + 0.0232299995f * fVec4[(IOTA0 - 47) & 255] + 0.0320556983f * fVec4[(IOTA0 - 46) & 255] + 0.0339844003f * fVec4[(IOTA0 - 45) & 255] + 0.0429931991f * fVec4[(IOTA0 - 44) & 255] + 0.0400390998f * fVec4[(IOTA0 - 43) & 255] + 0.0661742985f * fVec4[(IOTA0 - 42) & 255] + 0.0591797009f * fVec4[(IOTA0 - 41) & 255] + 0.0739867985f * fVec4[(IOTA0 - 40) & 255] + 0.0805054009f * fVec4[(IOTA0 - 39) & 255] + 0.123303004f * fVec4[(IOTA0 - 38) & 255] + 0.0540039018f * fVec4[(IOTA0 - 37) & 255] + 0.186620995f * fVec4[(IOTA0 - 36) & 255] + 0.0443480983f * fVec4[(IOTA0 - 35) & 255] + 0.164684996f * fVec4[(IOTA0 - 34) & 255] + 0.0312134009f * fVec4[(IOTA0 - 33) & 255] + 0.0267334003f * fVec4[(IOTA0 - 32) & 255] + 0.200696006f * fVec4[(IOTA0 - 27) & 255] + 0.399962991f * fVec4[(IOTA0 - 26) & 255] + 0.312427014f * fVec4[(IOTA0 - 23) & 255] + 0.0953003019f * fVec4[(IOTA0 - 21) & 255] + 0.0202147998f * fVec4[(IOTA0 - 19) & 255] + 0.00706786988f * fVec4[(IOTA0 - 17) & 255] + 0.000146484002f * fVec4[(IOTA0 - 8) & 255] + 0.000512694998f * fVec4[(IOTA0 - 6) & 255] + 0.000561523018f * fVec4[(IOTA0 - 2) & 255] + 0.000634766009f * fVec4[(IOTA0 - 4) & 255]) - (0.00252685999f * (fVec4[(IOTA0 - 84) & 255] + fVec4[(IOTA0 - 103) & 255] + fVec4[(IOTA0 - 105) & 255] + fVec4[(IOTA0 - 121) & 255] + fVec4[(IOTA0 - 123) & 255] + fVec4[(IOTA0 - 125) & 255] + fVec4[(IOTA0 - 127) & 255] + fVec4[(IOTA0 - 129) & 255] + fVec4[(IOTA0 - 131) & 255]) + 0.00251465011f * (fVec4[(IOTA0 - 97) & 255] + fVec4[(IOTA0 - 99) & 255] + fVec4[(IOTA0 - 101) & 255] + fVec4[(IOTA0 - 107) & 255] + fVec4[(IOTA0 - 109) & 255] + fVec4[(IOTA0 - 111) & 255] + fVec4[(IOTA0 - 113) & 255] + fVec4[(IOTA0 - 115) & 255] + fVec4[(IOTA0 - 117) & 255] + fVec4[(IOTA0 - 119) & 255]) + 0.00257567992f * fVec4[(IOTA0 - 108) & 255] + 0.00258789002f * (fVec4[(IOTA0 - 104) & 255] + fVec4[(IOTA0 - 106) & 255]) + 0.00260009989f * (fVec4[(IOTA0 - 90) & 255] + fVec4[(IOTA0 - 92) & 255] + fVec4[(IOTA0 - 94) & 255] + fVec4[(IOTA0 - 96) & 255] + fVec4[(IOTA0 - 98) & 255] + fVec4[(IOTA0 - 100) & 255] + fVec4[(IOTA0 - 102) & 255]) + 0.00250244001f * fVec4[(IOTA0 - 95) & 255] + 0.0024902299f * fVec4[(IOTA0 - 93) & 255] + 0.00247803004f * ((fVec4[(IOTA0 - 5) & 255] + fVec4[(IOTA0 - 7) & 255]) - fVec4[(IOTA0 - 15) & 255] + fVec4[(IOTA0 - 82) & 255] + fVec4[(IOTA0 - 91) & 255]) + 0.00246581994f * fVec4[(IOTA0 - 89) & 255] + 0.0024292001f * (fVec4[(IOTA0 - 80) & 255] + fVec4[(IOTA0 - 87) & 255]) + 0.00238036993f * fVec4[(IOTA0 - 85) & 255] + 0.00233153999f * fVec4[(IOTA0 - 83) & 255] + 0.00225830008f * fVec4[(IOTA0 - 81) & 255] + 0.00217285007f * fVec4[(IOTA0 - 79) & 255] + 0.00235595996f * fVec4[(IOTA0 - 78) & 255] + 0.00203856989f * fVec4[(IOTA0 - 77) & 255] + 0.00224608998f * fVec4[(IOTA0 - 76) & 255] + 0.00187987997f * fVec4[(IOTA0 - 75) & 255] + 0.00208740006f * fVec4[(IOTA0 - 74) & 255] + 0.00190429995f * fVec4[(IOTA0 - 72) & 255] + 0.0013916f * fVec4[(IOTA0 - 71) & 255] + 0.00104979996f * fVec4[(IOTA0 - 69) & 255] + 0.00134276995f * fVec4[(IOTA0 - 68) & 255] + 0.000610352028f * fVec4[(IOTA0 - 67) & 255] + 0.000915526995f * fVec4[(IOTA0 - 66) & 255] + 2.44140992e-05f * (fVec4[(IOTA0 - 13) & 255] + fVec4[(IOTA0 - 65) & 255]) + 0.000354004005f * fVec4[(IOTA0 - 64) & 255] + 0.00166016002f * (fVec4[(IOTA0 - 70) & 255] + fVec4[(IOTA0 - 73) & 255]) + 0.113098003f * fVec4[(IOTA0 - 31) & 255] + 0.222167999f * fVec4[(IOTA0 - 30) & 255] + 0.293029994f * fVec4[(IOTA0 - 29) & 255] + 0.146654993f * fVec4[(IOTA0 - 28) & 255] + 0.268029988f * fVec4[(IOTA0 - 25) & 255] + 0.208581999f * fVec4[(IOTA0 - 22) & 255] + 0.0471558012f * fVec4[(IOTA0 - 20) & 255] + 0.0159546006f * fVec4[(IOTA0 - 18) & 255] + 0.00822754018f * fVec4[(IOTA0 - 16) & 255] + 0.00438232021f * fVec4[(IOTA0 - 14) & 255] + 0.00202637003f * fVec4[(IOTA0 - 12) & 255] + 0.00145264005f * fVec4[(IOTA0 - 11) & 255] + 0.00062255899f * fVec4[(IOTA0 - 10) & 255] + 0.00207519997f * fVec4[(IOTA0 - 1) & 255] + 0.0023193399f * fVec4[(IOTA0 - 3) & 255] + 0.00219727005f * fVec4[(IOTA0 - 9) & 255] + 0.00255126995f * (fVec4[(IOTA0 - 124) & 255] + fVec4[(IOTA0 - 126) & 255] + fVec4[(IOTA0 - 128) & 255] + fVec4[(IOTA0 - 130) & 255]) + 0.00256348005f * (fVec4[(IOTA0 - 86) & 255] + fVec4[(IOTA0 - 110) & 255] + fVec4[(IOTA0 - 112) & 255] + fVec4[(IOTA0 - 114) & 255] + fVec4[(IOTA0 - 116) & 255] + fVec4[(IOTA0 - 118) & 255] + fVec4[(IOTA0 - 120) & 255] + fVec4[(IOTA0 - 122) & 255] + fVec4[(IOTA0 - 132) & 255])));
			fRec3[1] = fRec3[0];
			fRec5[1] = fRec5[0];
			fRec8[1] = fRec8[0];
			fRec12[1] = fRec12[0];
			fVec0[1] = fVec0[0];
			fRec14[1] = fRec14[0];
			fVec1[1] = fVec1[0];
			fRec13[1] = fRec13[0];
			fRec15[1] = fRec15[0];
			fRec11[2] = fRec11[1];
			fRec11[1] = fRec11[0];
			fRec10[1] = fRec10[0];
			fVec2[1] = fVec2[0];
			fRec9[1] = fRec9[0];
			fRec7[2] = fRec7[1];
			fRec7[1] = fRec7[0];
			fRec6[1] = fRec6[0];
			fVec3[1] = fVec3[0];
			fRec4[1] = fRec4[0];
			fRec2[2] = fRec2[1];
			fRec2[1] = fRec2[0];
			fRec1[1] = fRec1[0];
			fRec16[1] = fRec16[0];
			for (int j0 = 3; j0 > 0; j0 = j0 - 1) {
				fRec0[j0] = fRec0[j0 - 1];
			}
			IOTA0 = IOTA0 + 1;
		}
	}

};

#endif
