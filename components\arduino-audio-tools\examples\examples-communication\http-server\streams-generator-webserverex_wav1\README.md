# Webserver

With the help of the ESP32 WIFI functionality we can implement a simple web server. 
In the example we use a Sine Wave generator as sound source and return the result as an WAV file

The input is defied as part of the configuration

Multiple users can connect to the server!

## Dependencies

- https://github.com/pschatzmann/arduino-audio-tools
- https://github.com/pschatzmann/TinyHttp.git
