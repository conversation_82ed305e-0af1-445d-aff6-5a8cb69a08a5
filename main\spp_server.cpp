#include "spp_server.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"

// 引用统一的蓝牙设备名称定义
extern const char* BT_DEVICE_NAME;

static const char *TAG = "SPP_SERVER";

// 外部队列声明
extern QueueHandle_t spp_to_uart_queue;

// SPP连接句柄
static uint32_t spp_handle = 0;
static bool spp_connected = false;

// SPP事件回调
static void esp_spp_cb(esp_spp_cb_event_t event, esp_spp_cb_param_t *param)
{
    switch (event) {
    case ESP_SPP_INIT_EVT:
        ESP_LOGI(TAG, "ESP_SPP_INIT_EVT");
        esp_spp_start_srv(ESP_SPP_SEC_AUTHENTICATE, ESP_SPP_ROLE_SLAVE, 0, BT_DEVICE_NAME);
        break;
        
    case ESP_SPP_START_EVT:
        ESP_LOGI(TAG, "ESP_SPP_START_EVT");
        break;
        
    case ESP_SPP_SRV_OPEN_EVT:
        ESP_LOGI(TAG, "ESP_SPP_SRV_OPEN_EVT");
        spp_handle = param->srv_open.handle;
        spp_connected = true;
        break;
        
    case ESP_SPP_CLOSE_EVT:
        ESP_LOGI(TAG, "ESP_SPP_CLOSE_EVT");
        spp_connected = false;
        break;
        
    case ESP_SPP_DATA_IND_EVT:
        // 接收到SPP数据，转发到UART队列
        for (int i = 0; i < param->data_ind.len; i++) {
            uint8_t byte = param->data_ind.data[i];
            xQueueSend(spp_to_uart_queue, &byte, 0);
        }
        break;
        
    default:
        break;
    }
}

esp_err_t spp_server_init(void)
{
    esp_err_t ret;
    
    // 初始化SPP
    ret = esp_spp_register_callback(esp_spp_cb);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "spp register failed: %s", esp_err_to_name(ret));
        return ret;
    }
    
    ret = esp_spp_init(ESP_SPP_MODE_CB);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "spp init failed: %s", esp_err_to_name(ret));
        return ret;
    }
    
    ESP_LOGI(TAG, "SPP server initialized");
    return ESP_OK;
}

esp_err_t spp_server_send_data(uint8_t *data, uint16_t len)
{
    if (!spp_connected) {
        return ESP_ERR_INVALID_STATE;
    }
    
    return esp_spp_write(spp_handle, len, data);
}

bool spp_server_is_connected(void)
{
    return spp_connected;
}